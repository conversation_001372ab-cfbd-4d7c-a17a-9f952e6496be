using Conversations.Application.Abstractions;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Contracts;

namespace Conversations.Application.Chats.CreateChat;

public class CreateChatCommandHandler(
    IConversationDbContext dbContext,
    ISharedCustomerService customerService
) : IRequestHandler<CreateChatCommand, Result<Guid>>
{
    public async Task<Result<Guid>> Handle(CreateChatCommand request, CancellationToken cancellationToken)
    {
        // Check if chat with the same external ID and channel already exists
        var existingChat = await dbContext.Chat
            .FirstOrDefaultAsync(c => c.ExternalId == request.ExternalId && c.Channel == request.Channel,
                cancellationToken);

        if (existingChat != null)
        {
            return Result.Success(existingChat.Id);
        }

        // Create new chat
        var chat = new Chat(
            request.CustomerId,
            request.Channel,
            request.ExternalId,
            request.BaseChatId);

        if (request.CustomerId.HasValue)
        {
            var customer = await customerService.GetCustomerAsync(request.CustomerId.Value);
            if (customer != null && customer.IsSuccess)
            {
                chat.CustomerName = customer.Value.Name + " " + customer.Value.Surname;
            }
        }
        else
        {
            chat.CustomerName = request.CustomerName;
        }
        if (!string.IsNullOrEmpty(request.Title))
        {
            chat.SetTitle(request.Title);
        }

        if (request.AssignedUserId.HasValue)
        {
            chat.AssignTo(request.AssignedUserId.Value);
        }

        dbContext.Chat.Add(chat);
        await dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(chat.Id);
    }
}
