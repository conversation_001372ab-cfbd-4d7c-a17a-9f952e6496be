using MediatR;
using Shared.Application;

namespace Requests.Application.TicketComments.CreateTicketComment;

public record CreateTicketCommentCommand : IRequest<Result<Guid>>
{
    public Guid TicketId { get; init; }
    public required string Comment { get; init; }
    public List<CreateTicketCommentFileDto> Files { get; init; } = [];
}

public record CreateTicketCommentFileDto
{
    public Guid FileId { get; init; }
    public required string FileName { get; init; }
    public required string FilePath { get; init; }
}
