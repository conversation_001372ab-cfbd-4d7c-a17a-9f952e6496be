using Conversations.Domain.Chats;
using MediatR;
using Shared.Application;

namespace Conversations.Application.Chats.CreateChat;

public record CreateChatCommand : IRequest<Result<Guid>>
{
    public Guid? CustomerId { get; init; }
    public string CustomerName { get; set; }
    public ChatChannel Channel { get; init; }
    public string ExternalId { get; init; }
    public string BaseChatId { get; init; }
    public string? Title { get; init; }
    public Guid? AssignedUserId { get; init; }
}
