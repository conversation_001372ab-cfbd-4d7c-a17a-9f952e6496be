using Shared.Domain;

namespace Conversations.Domain.Chats;

public class Chat(
    Guid? customerId,
    ChatChannel channel,
    string externalId,
    string baseChatId
) : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid? CustomerId { get; private set; } = customerId;
    public string CustomerName { get; set; }
    public ChatChannel Channel { get; private set; } = channel;
    public string ExternalId { get; private set; } = externalId;
    public string BaseChatId { get; private set; } = baseChatId;
    public ChatStatus Status { get; private set; } = ChatStatus.Active;
    public DateTime StartedAt { get; private set; } = DateTime.Now;
    public DateTime? EndedAt { get; private set; }
    public Guid? AssignedUserId { get; private set; }
    public string? Title { get; private set; }
    private readonly List<ChatMessage> _messages = [];
    public IReadOnlyCollection<ChatMessage> Messages => _messages.AsReadOnly();

    public void AssignTo(Guid userId)
    {
        AssignedUserId = userId;
        Raise(new ChatAssignedEvent(Id, userId));
    }

    public void Close()
    {
        Status = ChatStatus.Closed;
        EndedAt = DateTime.Now;
        Raise(new ChatClosedEvent(Id));
    }

    public void Archive()
    {
        Status = ChatStatus.Archived;
        Raise(new ChatArchivedEvent(Id));
    }

    public ChatMessage AddMessage(string content, MessageDirection direction, string senderId, MessageContentType contentType)
    {
        var message = new ChatMessage(Id, content, direction, senderId, contentType)
        {
            Id = Guid.NewGuid()
        };
        _messages.Add(message);
        Raise(new ChatMessageAddedEvent(Id, message.Id));
        return message;
    }

    public void SetTitle(string title)
    {
        Title = title;
    }
}
