### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{baseUrl}}/api/v1/users/account/login
Content-Type: application/json

{
    "Email": "{{email}}",
    "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}


### Variables
@ticketId = 550e8400-e29b-41d4-a716-************

### Get Ticket List
GET {{baseUrl}}/api/v1/requests/tickets
Authorization: Bearer {{token}}
Content-Type: application/json

### Get Ticket by ID
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Create Ticket
POST {{baseUrl}}/api/v1/requests/tickets
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ticketType": 1,
  "subjectId": "550e8400-e29b-41d4-a716-************",
  "customerId": "550e8400-e29b-41d4-a716-************",
  "title": "Test Ticket",
  "description": "Test ticket description",
  "priority": 1,
  "departmentIds": ["550e8400-e29b-41d4-a716-************"],
  "tags": ["test", "api"]
}

### Update Ticket (PUT)
PUT {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "id": "{{ticketId}}",
  "ticketType": 1,
  "subjectId": "550e8400-e29b-41d4-a716-************",
  "title": "Updated Test Ticket",
  "description": "Updated test ticket description",
  "priority": 2,
  "departmentIds": ["550e8400-e29b-41d4-a716-************"],
  "tags": ["updated", "test"],
  "ticketFiles": []
}

### Patch Ticket (PATCH) - Update title and priority using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/title",
    "value": "Updated Ticket Title via JSON Patch"
  },
  {
    "op": "replace",
    "path": "/priority",
    "value": 3
  }
]

### Patch Ticket - Update only status using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/statusId",
    "value": "550e8400-e29b-41d4-a716-446655440004"
  }
]

### Patch Ticket - Update description using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/description",
    "value": "Updated description via JSON Patch"
  }
]

### Patch Ticket - Add tags using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/tags",
    "value": ["urgent", "patched", "json-patch"]
  }
]

### Patch Ticket - Update watchlist using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/watchlist",
    "value": ["550e8400-e29b-41d4-a716-446655440005", "550e8400-e29b-41d4-a716-446655440006"]
  }
]

### Patch Ticket - Update end date using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/endDate",
    "value": "2024-12-31T23:59:59Z"
  }
]

### Patch Ticket - Update attribute data using JSON Patch
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/attributeData",
    "value": {
      "customField1": "value1",
      "customField2": "value2"
    }
  }
]

### Patch Ticket - Multiple operations in one request
PATCH {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json-patch+json

[
  {
    "op": "replace",
    "path": "/title",
    "value": "Multi-operation Update"
  },
  {
    "op": "replace",
    "path": "/priority",
    "value": 2
  },
  {
    "op": "replace",
    "path": "/description",
    "value": "Updated via multiple JSON Patch operations"
  }
]

### Delete Ticket
DELETE {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}
Authorization: Bearer {{token}}
Content-Type: application/json

### Get Ticket History
GET {{baseUrl}}/api/v1/requests/tickets/{{ticketId}}/history
Authorization: Bearer {{token}}
Content-Type: application/json
