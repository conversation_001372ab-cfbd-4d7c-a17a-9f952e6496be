import { useSelector } from "react-redux";

import { RootState } from "@/store/Reducers";
import GeneralSearch from "@/apps/Common/GeneralSearch";
import { hanldleSetUserChatFilter } from "../ClientSideStates";



const Search = () => {
  const { userChatFilter} = useSelector((state: RootState) => state.chat);

  return (
    <>
      <GeneralSearch
      filter={userChatFilter}
      searchFieldName="searchTerm"
      filterActionFunc={hanldleSetUserChatFilter}
      filterKey="filter"

      />
    </>
  );
};

export default Search;
