using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.TicketComments.CreateTicketComment;

public class CreateTicketCommentCommandHandler : IRequestHandler<CreateTicketCommentCommand, Result<Guid>>
{
    private readonly IRequestsDbContext _dbContext;
    private readonly IWorkContext _workContext;
    private readonly ITicketHistoryService _historyService;

    public CreateTicketCommentCommandHandler(
        IRequestsDbContext dbContext,
        IWorkContext workContext,
        ITicketHistoryService historyService)
    {
        _dbContext = dbContext;
        _workContext = workContext;
        _historyService = historyService;
    }

    public async Task<Result<Guid>> Handle(CreateTicketCommentCommand request, CancellationToken cancellationToken)
    {
        // Ticket'ı kontrol et
        var ticketExists = await _dbContext.Tickets
            .AnyAsync(t => t.Id == request.TicketId, cancellationToken);

        if (!ticketExists)
        {
            return Result.Failure<Guid>("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // Yeni yorum oluştur
        var comment = new TicketComment
        {
            Id = Guid.NewGuid(),
            TicketId = request.TicketId,
            UserId = _workContext.UserId,
            Comment = request.Comment
        };

        _dbContext.TicketComments.Add(comment);

        // Dosyaları ekle
        if (request.Files.Any())
        {
            var ticketFiles = request.Files.Select(f => new TicketFile
            {
                Id = Guid.NewGuid(),
                TicketId = request.TicketId,
                CommentId = comment.Id,
                FileId = f.FileId,
                FileName = f.FileName,
                FilePath = f.FilePath
            }).ToList();

            _dbContext.TicketFiles.AddRange(ticketFiles);
        }

        // History tracking
        await _historyService.TrackTicketCommentAddedAsync(request.TicketId, request.Comment, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(comment.Id);
    }
}
