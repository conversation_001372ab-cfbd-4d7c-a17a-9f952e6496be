import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "antd";
import Header from "./Header";
import SendMessage from "./SendMessage";
import Messages from "./Messages";

const MessageDetailsIndex = () => {
  return (
    <>
      <Row>
        <Col xs={24}>
          <Header />
        </Col>
        <Col xs={24}>
          <Divider className="!m-0" />
        </Col>
        <Col xs={24} className=" !p-4 ">
          <Messages />
        </Col>
        <Col xs={24} className=" !fixed bottom-1 !w-[90%]" >
          <SendMessage />
        </Col>
      </Row>
    </>
  );
};

export default MessageDetailsIndex;
