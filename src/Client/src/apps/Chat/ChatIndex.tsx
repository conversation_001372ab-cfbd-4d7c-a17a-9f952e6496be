import { Col, Row } from "antd";
import UserListIndex from "./Components/LeftUserList/UserListIndex";
import MessageDetailsIndex from "./Components/RightMessageDetails/MessageDetailsIndex";
import TopOptions from "./Components/TopOptions";
import { useQueryClient } from "react-query";
import { useEffect } from "react";
import endPoints from "./EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const ChatIndex = () => {
  const queryClient = useQueryClient();
  const { selectedChatItem } = useSelector((state: RootState) => state.chat);

  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getUserChatListFilter,
      exact: false,
    });
  }, []);

  return (
    <Row className="min-h-screen">
      <Col xs={24}>
        <TopOptions />
      </Col>

      <Col xs={24} xl={6}>
        <UserListIndex />
      </Col>

      <Col xs={24} xl={18} className="flex flex-col h-screen border-l border-gray-300">
        {selectedChatItem ? (
          <MessageDetailsIndex />
        ) : (
          <div className="p-4 text-center">Bir chat seç</div>
        )}
      </Col>
    </Row>
  );
};

export default ChatIndex;
