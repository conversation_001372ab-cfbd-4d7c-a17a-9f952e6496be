import { createSlice } from "@reduxjs/toolkit";

const InitialState: { userChatFilter: any; selectedChatItem: null | any,chatDetailsFilter:any; } = {
  userChatFilter: {
    PageNumber: 1,
    PageSize: 20,
    Status: 1,
  },
  chatDetailsFilter: {
    PageNumber: 1,
    PageSize: 20,
    Status: 1,
  },
  selectedChatItem: null,
};

const chatSlice = createSlice({
  name: "ChatSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetUserChatFilter: (state, action) => {
      let data = action.payload;
      state.userChatFilter = data.filter;
    },
    handleSetChatDetailsFilter: (state, action) => {
      let data = action.payload;
      state.chatDetailsFilter= data.filter;
    },

    hanldleSetSelectedUserListChatItem: (state, action) => {
      let data = action.payload;
      state.selectedChatItem = data.data;
    },

    handleResetAllFieldsChat: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetUserChatFilter: (state) => {
      state.userChatFilter = {
        PageNumber: 1,
        PageSize: 20,
      };
    },
  },
});

export const {
  handleResetAllFieldsChat,
  handleResetUserChatFilter,
  hanldleSetUserChatFilter,
  hanldleSetSelectedUserListChatItem,
  handleSetChatDetailsFilter
} = chatSlice.actions;
export default chatSlice;
