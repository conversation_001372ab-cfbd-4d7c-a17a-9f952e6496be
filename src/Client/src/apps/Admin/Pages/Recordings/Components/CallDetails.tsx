import { FC, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Col, Table, Tooltip, Typography } from "antd";
import { useGet3cxRecordingDetails } from "../ServerSideStates";
import dayjs from "dayjs";
import { DownloadOutlined, FormOutlined } from "@ant-design/icons";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import ExpandableText from "@/apps/Common/TruncatedDesc";
import { get3cxCallRecordData } from "../Services";

const CallDetails: FC<{ recordId: string }> = ({ recordId }) => {
  const { t } = useTranslation();

  const { Text } = Typography;
  const callDetails = useGet3cxRecordingDetails(recordId);

  // WAV dosyası indirme fonksiyonu
  const handleDownloadRecording = async (record: any) => {
    const link = record?.RecordingUrl;

    if (!link) {
      openNotificationWithIcon("error", t("recording.invalidLink"));
      return;
    }

    try {
      const response = await get3cxCallRecordData(link);

      // Blob oluştur - response zaten wav content-type ile geliyor
      const blob = new Blob([response.data], {
        type: "audio/wav",
      });

      // İndirme URL'si oluştur
      const downloadUrl = window.URL.createObjectURL(blob);
      const anchor = document.createElement("a");
      anchor.href = downloadUrl;

      // Dinamik dosya adı (participant adı + tarih)
      const fileName = `${record.SourceParticipantName || 'recording'}_${dayjs(record.StartTime).format('YYYY-MM-DD_HH-mm')}.wav`;
      anchor.download = fileName;

      document.body.appendChild(anchor);
      anchor.click();
      anchor.remove();

      // Memory temizliği
      window.URL.revokeObjectURL(downloadUrl);

      // Başarı mesajı
      openNotificationWithIcon("success", t("form.transactionSuccessful"));

    } catch (error) {
      openNotificationWithIcon("error", t("form.transactionFaild"));
    }
  };

  const columns = [
    {
      title: t("recording.sourceParticipantName"),
      dataIndex: "SourceParticipantName",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("recording.sourceParticipantPhoneNumber"),
      dataIndex: "SourceParticipantPhoneNumber",
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("recording.startTime"),
      dataIndex: "StartTime",
      key: "StartTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.endTime"),
      dataIndex: "EndTime",
      key: "EndTime",
      render: (value: string) => {
        return (
          <>
            {value && (
              <Text className="!text-xs">
                {dayjs(value).format("YYYY-DD-MM HH:mm")}
              </Text>
            )}
          </>
        );
      },
    },
    {
      title: t("recording.summary"),
      dataIndex: "Summary",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={t("recording.summary")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: t("recording.fullContent"),
      dataIndex: "Transcription",
      render: (value: string, record: any) => {
        return (
          <>
            <ExpandableText
              title={t("recording.fullContent")}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex justify-end mr-4">
          {record?.RecordingUrl && (
            <Tooltip title={t("recording.download")}>
              <DownloadOutlined
                className="!text-gray-500"
                onClick={() => handleDownloadRecording(record)}
              />
            </Tooltip>
          )}
        </Col>
      ),
    },
  ];

  return (
    <>
      <Table
        loading={callDetails.isLoading || callDetails.isFetching}
        dataSource={callDetails?.data?.Value || []}
        columns={columns}
        pagination={false}
      />
    </>
  );
};

export default CallDetails;