import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  FormOutlined,
  EyeOutlined,
  ExclamationCircleOutlined, // Critical - 4
  WarningOutlined, // High - 3
  MinusCircleOutlined, // Medium - 2
  CheckCircleOutlined, // Low - 1
} from "@ant-design/icons";
import {
  Col,
  Drawer,
  Dropdown,
  Modal,
  Table,
  Tag,
  Tooltip,
  Typography,
  Form,
  Button,
  Space,
} from "antd";
import endPoints from "../EndPoints";
import { useQueryClient } from "react-query";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { useGetTickets } from "../ServerSideStates";
import { deleteTicket, getTicketForDetail, updateTicketWithPut } from "../Services";
import {
  hanldleSetTicketDetails,
  hanldleSetTicketFilter,
  hanldleSetTicketPageType,
} from "../ClientSideStates";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import {
  determineTicketNotificationWay,
  determineTicketPriority,
} from "@/helpers/Ticket";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import dayjs from "dayjs";
import { excludeUnnesessaryKey } from "@/helpers/ExcludeUnNesessaryKey";
import AddOrUpdateIndex from "./AddOrUpdateIndex";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import relativeTime from "dayjs/plugin/relativeTime";
import "dayjs/locale/tr";

dayjs.extend(relativeTime);

const ListItems = () => {
  const { Text } = Typography;
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowWatchlistModal, setIsShowWatchlistModal] = useState(false);
  const [watchlistRecord, setWatchlistRecord] = useState<any | null>(null);
  const [watchlistForm] = Form.useForm();
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { filter, pageType, ticketDetails } = useSelector((state: RootState) => state.ticket);
  const tickets = useGetTickets(excludeUnnesessaryKey(filter));
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  dayjs.extend(relativeTime);
  dayjs.locale("tr");

  // Priority ikonlarını belirleyen fonksiyon
  const getPriorityIcon = (priority: number) => {
    const iconStyle = { fontSize: '12px', marginRight: '6px' };

    switch (priority) {
      case 4: // Critical
        return (
          <Tooltip title={t("ticket.list.critical")}>
            <ExclamationCircleOutlined
              style={{ ...iconStyle, color: '#ff4d4f' }} // Kırmızı
            />
          </Tooltip>
        );
      case 3: // High
        return (
          <Tooltip title={t("ticket.list.high")}>
            <WarningOutlined
              style={{ ...iconStyle, color: '#fa8c16' }} // Turuncu
            />
          </Tooltip>
        );
      case 2: // Medium
        return (
          <Tooltip title={t("ticket.list.medium")}>
            <MinusCircleOutlined
              style={{ ...iconStyle, color: '#1890ff' }} // Mavi
            />
          </Tooltip>
        );
      case 1: // Low
      default:
        return (
          <Tooltip title={t("ticket.list.low")}>
            <CheckCircleOutlined
              style={{ ...iconStyle, color: '#52c41a' }} // Yeşil
            />
          </Tooltip>
        );
    }
  };

  // Watchlist ekleme fonksiyonu
  const handleAddCurrentUserToWatchlist = async (record: any) => {
    try {
      const selectedUserId = userInfoes?.Id;
      const currentWatchlist = ticketDetails?.Watchlist || [];

      const currentUserIds = currentWatchlist.map((user: any) =>
        user.UserId || user.Id || user
      );

      const isUserAlreadyInWatchlist = currentUserIds.includes(selectedUserId);

      if (isUserAlreadyInWatchlist) {
        openNotificationWithIcon("warning", "Zaten takip listesindesiniz!");
        return;
      }

      const newWatchlist = [...currentUserIds, selectedUserId];

      const updatedTicket = {
        Id: record.Id,
        Code: record.Code,
        Title: record.Title,
        Description: record.Description,
        SubjectId: record.SubjectId,
        CustomerId: record.CustomerId,
        UserId: record.UserId,
        StatusId: record.StatusId,
        Priority: record.Priority,
        EndDate: record.EndDate,
        NotificationWayId: record.NotificationWayId,
        TopTicketId: record.TopTicketId,
        Address: record.Address,
        AttributeData: record.AttributeData,
        TicketFiles: record.TicketFiles || [],
        Watchlist: newWatchlist,
        DepartmentIds: record.Departments?.map((dept: any) =>
          dept.DepartmentId || dept.Id
        ) || [],
      };

      await updateTicketWithPut(updatedTicket);
      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });

      openNotificationWithIcon("success", "Takip listesine eklendiniz!");

    } catch (error: any) {
      showErrorCatching(error, null, false, t);
    }
  };

  const columns = [
    {
      title: t("ticket.list.Code"),
      dataIndex: "Code",
      key: "Code",
      width: "8%", // Width artırıldı çünkü icon ekledik
      sorter: (a: any, b: any) => a?.Code.localeCompare(b?.Code),
      render: (value: string, record: any) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* Priority ikonu */}
            {getPriorityIcon(record.Priority)}
            {/* Kod */}
            <Text className="!text-xs">{value}</Text>
          </div>
        );
      },
    },
    {
      title: t("ticket.list.title"),
      dataIndex: "Title",
      key: "Title",
      width: "18%", // Width artırıldı
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.subject"),
      dataIndex: "SubjectName",
      key: "Subject",
      width: "10%",
      sorter: (a: any, b: any) => a?.SubjectName.localeCompare(b?.SubjectName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("task.list.assignedUser"),
      dataIndex: "UserName",
      key: "UserName",
      width: "15%",
      sorter: (a: any, b: any) => a?.UserName.localeCompare(b?.UserName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.customer"),
      dataIndex: "CustomerName",
      key: "CustomerName",
      width: "15%",
      sorter: (a: any, b: any) =>
        a?.CustomerName.localeCompare(b?.CustomerName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.statusName"),
      dataIndex: "StatusName",
      key: "StatusName",
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.StatusName.localeCompare(b?.StatusName),
      render: (value: string) => {
        return (
          <>
            <Text className="!text-xs">{value}</Text>
          </>
        );
      },
    },
    {
      title: t("ticket.list.departments"),
      dataIndex: "Departments",
      key: "Departments",
      width: "15%",
      render: (value: any) => {
        return (
          <>
            {value?.length > 0 && (
              <>
                {value?.map((item: any) => {
                  return (
                    <span key={item.Id}>
                      <Tag color="#0096d1" className="m-1">{item?.Name || ""}</Tag>
                    </span>
                  );
                })}
              </>
            )}
          </>
        );
      },
    },
    // Priority kolonu KALDIRILDI - artık Kod kolonunda ikон olarak gösteriliyor
    {
      title: t("ticket.list.sslDate"),
      dataIndex: "InsertDate",
      key: "InsertDate",
      width: "10%",
      render: (value: number) => {
        const now = dayjs();
        const insertDate = dayjs(value);
        const diffInHours = now.diff(insertDate, "hours");
        const diffInDays = now.diff(insertDate, "days");

        const humanized =
          diffInDays < 1
            ? insertDate.fromNow(true) + " önce" // Örn: '5 saat önce'
            : `${diffInDays} gün önce`; // Örn: '3 gün önce'

        return (
          <Text className="!text-xs">
            {humanized}
          </Text>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="!flex gap-2 justify-end !px-2">
          {/* Watchlist göz ikonu */}
          <Tooltip title="Takip Listesine Ekle">
            <EyeOutlined
              className="!text-[#1890ff] !text-sm cursor-pointer hover:!text-[#1890ff]"
              onClick={async (e) => {
                e.preventDefault()
                e.stopPropagation()
                await handleAddCurrentUserToWatchlist(record);
              }}
            />
          </Tooltip>

          <Tooltip title={t("ticket.list.edit")}>
            <FormOutlined
              className="!text-[#0096d1] !text-sm cursor-pointer hover:!text-[#1890ff]"
              onClick={async (e) => {
                console.log("Record", record);
                e.preventDefault();
                e.stopPropagation();

                await dispatch(hanldleSetTicketDetails({ data: record }));

                queryClient.resetQueries({
                  queryKey: endPoints.getTicketComments,
                  exact: false,
                });
                setIsShowEditDrawer(true);
              }}
            />
          </Tooltip>

          <Tooltip title={t("ticket.list.delete")}>
            <DeleteOutlined
              className="!text-[#9da3af] !text-sm cursor-pointer hover:!text-[#ff4d4f]"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                confirm(record);
              }}
            />
          </Tooltip>
        </Col>
      ),
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: t("ticket.list.warning"),
      icon: null,
      content: t("ticket.list.deleteModalDesc"),
      okText: t("ticket.list.delete"),
      cancelText: t("ticket.list.cancel"),
      onOk: async () => {
        try {
          await deleteTicket(record);
          openNotificationWithIcon("success", t("form.transactionSuccessful"));
          queryClient.resetQueries({
            queryKey: endPoints.getTicketListFilter,
            exact: false,
          });
        } catch (error: any) {
          showErrorCatching(error, null, false, t);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageNumber: pageNum, PageSize: pageSize };
    dispatch(hanldleSetTicketFilter({ filter: newFilter }));
  };

  return (
    <>
      <Table
        columns={columns}
        dataSource={tickets?.data?.Value}
        scroll={{ x: 700 }}
        loading={tickets.isLoading || tickets.isFetching}
        onRow={(record) => {
          return {
            onClick: async (event) => {
              await dispatch(hanldleSetTicketDetails({ data: record }));
              queryClient.resetQueries({
                queryKey: endPoints.getTicketDetail,
                exact: false,
              });
              setIsShowEditDrawer(true);
            },
          };
        }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: tickets.data?.FilteredCount || 0,
          current: tickets.data?.PageNumber,
          pageSize: tickets.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
        rowKey={"Id"}
      />

      {/* Edit Drawer */}
      <Drawer
        width={ticketDetails != null ? "80%" : "40%"}
        title={t("ticket.list.editTicket") + " (" + ticketDetails?.Code + ")"}
        open={isShowEditDrawer}
        onClose={() => setIsShowEditDrawer(false)}
      >
        <AddOrUpdateIndex />
      </Drawer>
    </>
  );
};

export default ListItems;