import { <PERSON>, <PERSON>vide<PERSON>,  <PERSON> } from "antd";
import Search from "./Search";


const TopOptions = () => {
  // const [isShowNewConversation, setIsShowNewConversation] = useState(false);
  // const [isShowCreateGroupChat, setIsShowCreateGroupChat] = useState(false);
  // const { t } = useTranslation();
  // const items: MenuProps["items"] = [
  //   {
  //     key: "1",
  //     label: t("chat.startChat"),
  //     onClick: () => {
  //       setIsShowNewConversation(true);
  //     },
  //   },
  //   {
  //     key: "2",
  //     label: t("chat.createGroupChat"),
  //     onClick: () => {
  //       setIsShowCreateGroupChat(true);
  //     },
  //   },
  // ];

  return (
    <>
      <Row className="menu-height">
        <Col xs={24} className="!flex gap-1 items-center user">
          {/* <div className="!flex items-center justify-center !w-[30px] !h-full">
            <Dropdown
              menu={{ items }}
              trigger={["click"]}
              placement="bottomLeft"
            >
              <div className="flex items-center justify-center w-[30px] h-full cursor-pointer">
                <PlusOutlined className="text-base text-gray-300" />
              </div>
            </Dropdown>
          </div> */}
          <Search />
        </Col>
        <Col xs={24}>
          <Divider className="!m-0 !text-gray-400" />
        </Col>
      </Row>

      {/* <Modal
        open={isShowNewConversation}
        onCancel={() => {
          setIsShowNewConversation(false);
        }}
        footer={false}
      >
        <StartChat />
      </Modal>

      <Modal
        open={isShowCreateGroupChat}
        onCancel={() => {
          setIsShowCreateGroupChat(false);
        }}
        footer={false}
      >
        <CreateGroupChat />
      </Modal> */}
    </>
  );
};

export default TopOptions;
