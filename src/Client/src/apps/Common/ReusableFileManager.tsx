import React, { useState } from 'react';
import { Card, Space, Button, Image, Modal, Typography } from 'antd';
import { DeleteOutlined, PlusOutlined, EyeOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import FileManagerIndex from "@/apps/FileManager/FileManagerIndex";
import { handleResetAllFieldsFolder } from "@/apps/FileManager/ClientSideStates";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";

const { Text } = Typography;

// Dosya tipi tanımı
export interface FileItem {
    FileId: string;
    FileName: string;
    FilePath: string;
    MimeType?: string;
    FileSizeInBytes?: number;
}

interface ReusableFileManagerProps {
    fileList: FileItem[];
    onFileListChange: (files: FileItem[]) => void;
    title?: string;
    showCount?: boolean;
    maxFiles?: number;
    acceptedFileTypes?: string[];
    disabled?: boolean;
    className?: string;
    style?: React.CSSProperties;
    cardSize?: 'small' | 'default';
    imageSize?: {
        width: number;
        height: number;
    };
    showEmptyMessage?: boolean;
    emptyMessage?: string;
}

const ReusableFileManager: React.FC<ReusableFileManagerProps> = ({
    fileList = [],
    onFileListChange,
    title = "Dosyalar",
    showCount = true,
    maxFiles,
    acceptedFileTypes,
    disabled = false,
    className = "",
    style = {},
    cardSize = "small",
    imageSize = { width: 80, height: 80 },
    showEmptyMessage = true,
    emptyMessage = "Henüz dosya eklenmedi. Dosya eklemek için yukarıdaki + butonuna tıklayın."
}) => {
    const [isShowFileUploaderModal, setIsShowFileUploaderModal] = useState(false);
    const dispatch = useDispatch();

    // Dosya boyutunu formatlamak için yardımcı fonksiyon
    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Dosya tipine göre ikon döndüren fonksiyon
    const getFileIcon = (mimeType: string) => {
        if (mimeType?.startsWith('image/')) {
            return '🖼️';
        } else if (mimeType?.includes('pdf')) {
            return '📄';
        } else if (mimeType?.includes('word')) {
            return '📝';
        } else if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet')) {
            return '📊';
        } else if (mimeType?.includes('video/')) {
            return '🎥';
        } else if (mimeType?.includes('audio/')) {
            return '🎵';
        } else if (mimeType?.includes('zip') || mimeType?.includes('rar')) {
            return '📦';
        } else {
            return '📎';
        }
    };

    // Görsel dosya olup olmadığını kontrol eden fonksiyon
    const isImageFile = (mimeType: string) => {
        return mimeType?.startsWith('image/');
    };

    // Dosya uzantısından mime type çıkarma
    const getMimeTypeFromFileName = (fileName: string): string => {
        const extension = fileName.split('.').pop()?.toLowerCase();

        switch (extension) {
            case 'jpg':
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'gif':
                return 'image/gif';
            case 'webp':
                return 'image/webp';
            case 'svg':
                return 'image/svg+xml';
            case 'pdf':
                return 'application/pdf';
            case 'doc':
                return 'application/msword';
            case 'docx':
                return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            case 'xls':
                return 'application/vnd.ms-excel';
            case 'xlsx':
                return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            case 'ppt':
                return 'application/vnd.ms-powerpoint';
            case 'pptx':
                return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
            case 'txt':
                return 'text/plain';
            case 'zip':
                return 'application/zip';
            case 'rar':
                return 'application/x-rar-compressed';
            case 'mp4':
                return 'video/mp4';
            case 'mp3':
                return 'audio/mpeg';
            case 'wav':
                return 'audio/wav';
            default:
                return 'application/octet-stream';
        }
    };

    // Dosya listesini normalize eden fonksiyon
    const normalizeFileList = (files: any[]): FileItem[] => {
        return files.map(file => ({
            FileId: file.FileId || file.Id,
            FileName: file.FileName || file.OriginalFileName,
            FilePath: file.FilePath || file.StoragePath,
            MimeType: file.MimeType || getMimeTypeFromFileName(file.FileName || file.OriginalFileName),
            FileSizeInBytes: file.FileSizeInBytes
        }));
    };

    // Dosya seçimi tamamlandığında çalışacak fonksiyon
    const handleFileSelection = (selectedFiles: any) => {
        const filesArray = Object.values(selectedFiles).map((file: any) => ({
            FileId: file.Id,
            FileName: file.FileName || file.OriginalFileName,
            FilePath: file.StoragePath,
            MimeType: file.MimeType || getMimeTypeFromFileName(file.FileName || file.OriginalFileName),
            FileSizeInBytes: file.FileSizeInBytes
        }));

        const existingIds = fileList.map(f => f.FileId);
        const newFiles = filesArray.filter((file: any) => !existingIds.includes(file.FileId));

        // MaxFiles kontrolü
        let updatedFileList = [...fileList, ...newFiles];
        if (maxFiles && updatedFileList.length > maxFiles) {
            updatedFileList = updatedFileList.slice(0, maxFiles);
        }

        onFileListChange(updatedFileList);
        setIsShowFileUploaderModal(false);
        dispatch(handleResetAllFieldsFolder());
    };

    // Dosya silme
    const handleRemoveFile = (fileId: string) => {
        const updatedFileList = fileList.filter(f => f.FileId !== fileId);
        onFileListChange(updatedFileList);
    };

    // Dosya ekleme butonuna tıklama
    const handleAddFiles = () => {
        if (maxFiles && fileList.length >= maxFiles) {
            // Maximum dosya sayısına ulaşıldı uyarısı gösterilebilir
            return;
        }
        setIsShowFileUploaderModal(true);
    };

    // Dosya listesini normalize et
    const normalizedFileList = normalizeFileList(fileList);

    // Maximum dosya kontrolü
    const canAddMoreFiles = !maxFiles || fileList.length < maxFiles;

    return (
        <>
            <Card
                size={cardSize}
                className={`!border-none !rounded-none ${className}`}
                style={style}
            >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    {/* Başlık */}
                    <div>
                        <Text strong style={{ fontSize: '12px', marginBottom: '0px', display: 'block' }}>
                            {title}
                            {showCount && normalizedFileList.length > 0 && (
                                <span style={{ color: '#64748b', fontWeight: 'normal' }}>
                                    {' '}({normalizedFileList.length}
                                    {maxFiles && `/${maxFiles}`})
                                </span>
                            )}
                        </Text>

                        {/* Dosya listesi */}
                        <div style={{
                            display: 'flex',
                            gap: '8px',
                            overflowX: 'auto',
                            padding: '2px 0',
                            scrollbarWidth: 'thin',
                            scrollbarColor: '#d1d5db #f3f4f6'
                        }}>
                            {/* Dosya ekleme butonu */}
                            {canAddMoreFiles && !disabled && (
                                <div
                                    style={{
                                        minWidth: `${imageSize.width}px`,
                                        height: `${imageSize.height}px`,
                                        border: '2px dashed #d1d5db',
                                        borderRadius: '8px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        cursor: 'pointer',
                                        backgroundColor: '#f9fafb',
                                        transition: 'all 0.2s ease',
                                        flexShrink: 0,
                                        order: 1
                                    }}
                                    className="hover:border-blue-400 hover:bg-blue-50"
                                    onClick={handleAddFiles}
                                >
                                    <PlusOutlined style={{ fontSize: '24px', color: '#9ca3af' }} />
                                </div>
                            )}

                            {/* Dosyalar */}
                            <Image.PreviewGroup>
                                {normalizedFileList.map((file, index) => (
                                    <div
                                        key={file.FileId}
                                        style={{
                                            position: 'relative',
                                            width: `${imageSize.width}px`,
                                            height: `${imageSize.height}px`,
                                            borderRadius: '8px',
                                            overflow: 'hidden',
                                            flexShrink: 0,
                                            border: '1px solid #e5e7eb',
                                            order: index + 2
                                        }}
                                    >
                                        {/* Silme butonu */}
                                        {!disabled && (
                                            <Button
                                                type="text"
                                                size="small"
                                                danger
                                                icon={<DeleteOutlined style={{ fontSize: '12px' }} />}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleRemoveFile(file.FileId);
                                                }}
                                                style={{
                                                    position: 'absolute',
                                                    top: '4px',
                                                    right: '4px',
                                                    zIndex: 2,
                                                    width: '24px',
                                                    height: '24px',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                                    borderRadius: '50%',
                                                    backdropFilter: 'blur(4px)'
                                                }}
                                            />
                                        )}

                                        {/* Dosya içeriği */}
                                        {isImageFile(file.MimeType || '') ? (
                                            // Görsel dosya
                                            <Image
                                                width="100%"
                                                height="100%"
                                                src={`${setBackEndUrl()}/Uploads/${file.FilePath}`}
                                                style={{
                                                    objectFit: 'cover',
                                                    cursor: 'pointer'
                                                }}
                                                preview={{
                                                    src: `${setBackEndUrl()}/Uploads/${file.FilePath}`,
                                                    mask: <div style={{
                                                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        color: 'white',
                                                        fontSize: '12px'
                                                    }}>
                                                        <EyeOutlined />
                                                    </div>
                                                }}
                                            />
                                        ) : (
                                            // Görsel olmayan dosya
                                            <div
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    backgroundColor: '#f8fafc',
                                                    cursor: 'pointer'
                                                }}
                                                onClick={() => {
                                                    window.open(`${setBackEndUrl()}/Uploads/${file.FilePath}`, '_blank');
                                                }}
                                            >
                                                <div style={{ fontSize: '24px', marginBottom: '4px' }}>
                                                    {getFileIcon(file.MimeType || '')}
                                                </div>
                                                <div style={{
                                                    fontSize: '8px',
                                                    color: '#64748b',
                                                    textAlign: 'center',
                                                    padding: '0 2px'
                                                }}>
                                                    {file.FileName.split('.').pop()?.toUpperCase()}
                                                </div>
                                            </div>
                                        )}

                                        {/* Dosya adı tooltip */}
                                        <div
                                            style={{
                                                position: 'absolute',
                                                bottom: '0',
                                                left: '0',
                                                right: '0',
                                                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                                color: 'white',
                                                padding: '4px 6px',
                                                fontSize: '10px',
                                                textAlign: 'center',
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis'
                                            }}
                                            title={file.FileName}
                                        >
                                            {file.FileName}
                                        </div>
                                    </div>
                                ))}
                            </Image.PreviewGroup>
                        </div>
                    </div>

                    {/* Dosya yoksa mesaj */}
                    {showEmptyMessage && normalizedFileList.length === 0 && (
                        <div style={{
                            textAlign: 'center',
                            color: '#64748b',
                            padding: '5px 10px',
                            backgroundColor: '#f8fafc',
                            borderRadius: '8px',
                            border: '1px dashed #cbd5e1'
                        }}>
                            <p style={{ margin: 0, fontSize: '14px' }}>
                                {emptyMessage}
                            </p>
                        </div>
                    )}
                </Space>
            </Card>

            {/* File Upload Modal */}
            <Modal
                open={isShowFileUploaderModal}
                onCancel={() => {
                    setIsShowFileUploaderModal(false);
                    dispatch(handleResetAllFieldsFolder());
                }}
                footer={false}
                width="90%"
                title="Dosya Seçin"
            >
                <FileManagerIndex
                    onFinishSelectFile={handleFileSelection}
                />
            </Modal>
        </>
    );
};

export default ReusableFileManager;