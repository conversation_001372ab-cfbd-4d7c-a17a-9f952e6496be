namespace Requests.Application;

public record TicketCommentDto
{
    public Guid Id { get; init; }
    public Guid TicketId { get; init; }
    public Guid UserId { get; init; }
    public string? UserName { get; init; }
    public string Comment { get; init; } = string.Empty;
    public DateTime InsertDate { get; init; }
    public List<TicketCommentFileDto> Files { get; init; } = [];
}

public record TicketCommentFileDto
{
    public Guid Id { get; init; }
    public Guid FileId { get; init; }
    public string FileName { get; init; } = string.Empty;
    public string FilePath { get; init; } = string.Empty;
}
