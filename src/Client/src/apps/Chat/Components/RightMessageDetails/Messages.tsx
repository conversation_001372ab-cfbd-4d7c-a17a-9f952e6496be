import { RootState } from "@/store/Reducers";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { useGetChatMessageDetails } from "../../ServerSideStates";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
  HubConnection,
} from "@microsoft/signalr";
import { useEffect, useRef, useState } from "react";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { Avatar } from "antd";
import MessageItemOptions from "./MessageItemOptions";

const Messages = () => {
  const [connection, setConnection] = useState<HubConnection | null>(null);
  const { selectedChatItem,selectedMessageItem } = useSelector((state: RootState) => state.chat);
  const chatDetails = useGetChatMessageDetails(selectedChatItem?.Id);
  const messages = chatDetails?.data?.Value?.Messages || [];
  const messagesLength = messages.length;
  const incomingName = chatDetails?.data?.Value?.CustomerName || "";
  const initialsIncoming = incomingName
    .split(" ")
    .map((n: any) => n[0])
    .join("")
    .toUpperCase();

  const queryClient = useQueryClient();
  const lastMessageRef = useRef<HTMLDivElement | null>(null);

  // SignalR bağlantısı
  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/chathub`, {
        accessTokenFactory: () => localStorage.getItem("access_token") || "",
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    newConnection
      .start()
      .then(() => setConnection(newConnection))
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    return () => {
      newConnection.stop();
    };
  }, []);

  // Yeni mesaj geldiğinde refetch
  useEffect(() => {
    if (connection) {
      connection.on("NewMessage", () => {
        queryClient.resetQueries({
          queryKey: endPoints.getChatMessageDetails,
          exact: false,
        });
      });
    }

    return () => {
      if (connection) {
        connection.off("NewMessage");
      }
    };
  }, [connection]);

  // Scroll mesaj konteyneri içinde en alta
  useEffect(() => {
    if (lastMessageRef.current) {
      lastMessageRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  }, [messagesLength]);

  return (
    <div className="h-[calc(100vh-200px)] overflow-y-auto px-4" id="message-container">
      <div className="space-y-4 py-4">
        {messages.map((msg: any, index: number) => {
          const isLast = index === messagesLength - 1
         

          return (
            <div
              key={index}
              ref={isLast ? lastMessageRef : null}
              className={`flex items-start space-x-1 ${
                msg.Direction !== "Incoming" ? "justify-end" : "justify-start"
              }`}
            >
            
              {msg.Direction === "Incoming" && (
                <Avatar className="!bg-[#58666e] !text-white">
                  {initialsIncoming}
                </Avatar>
              )}

              <div
                className={`p-4 rounded-lg max-w-xs flex flex-col gap-1 !relative ${
                  msg.Direction !== "Incoming"
                    ? "bg-gray-200 text-gray-900"
                    : "bg-yellow-100 text-gray-900"
                }`}
              >
                 <div className={`!flex justify-end !w-full !absolute right-0`} >
                 <MessageItemOptions item={msg} />
                 </div>
                <p className="text-xs">{msg.Content}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {dayjs(msg.SentAt).format("DD.MM.YYYY HH:mm")}
                </p>
              </div>

              {msg.Direction !== "Incoming" && (
                <Avatar className="!bg-gray-200 !text-black">
                  {msg?.SenderName || ""}
                </Avatar>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Messages;
