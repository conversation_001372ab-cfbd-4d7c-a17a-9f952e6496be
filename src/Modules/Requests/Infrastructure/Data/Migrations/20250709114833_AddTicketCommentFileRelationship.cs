﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddTicketCommentFileRelationship : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CommentId",
                schema: "Requests",
                table: "TicketFiles",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TicketFiles_CommentId",
                schema: "Requests",
                table: "TicketFiles",
                column: "CommentId");

            migrationBuilder.AddForeignKey(
                name: "FK_TicketFiles_TicketComments_CommentId",
                schema: "Requests",
                table: "TicketFiles",
                column: "CommentId",
                principalSchema: "Requests",
                principalTable: "TicketComments",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TicketFiles_TicketComments_CommentId",
                schema: "Requests",
                table: "TicketFiles");

            migrationBuilder.DropIndex(
                name: "IX_TicketFiles_CommentId",
                schema: "Requests",
                table: "TicketFiles");

            migrationBuilder.DropColumn(
                name: "CommentId",
                schema: "Requests",
                table: "TicketFiles");
        }
    }
}
