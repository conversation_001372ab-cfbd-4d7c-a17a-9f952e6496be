using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class TicketFileConfiguration : IEntityTypeConfiguration<TicketFile>
{
    public void Configure(EntityTypeBuilder<TicketFile> builder)
    {
        builder.ToTable("TicketFiles");

        builder.<PERSON><PERSON><PERSON>(t => t.Id);

        builder.Property(t => t.FileId)
            .IsRequired();

        builder.Property(t => t.FileName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(t => t.FilePath)
            .IsRequired();

        builder.HasOne<Ticket>()
            .WithMany(t => t.TicketFiles)
            .HasForeignKey(t => t.TicketId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne<TicketComment>()
            .WithMany(c => c.Files)
            .HasForeignKey(t => t.CommentId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
