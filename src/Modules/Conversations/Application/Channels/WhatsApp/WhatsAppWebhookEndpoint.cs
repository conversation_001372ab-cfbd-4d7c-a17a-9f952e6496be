using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Shared.Endpoints;

namespace Conversations.Application.Channels.WhatsApp;

internal sealed class WhatsAppWebhookEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v1/conversations/webhooks/whatsapp", async (
            [FromBody] WhatsAppDto whatsapp,
            WhatsAppWebhookHandler handler,
            ILogger<WhatsAppWebhookEndpoint> logger)
            =>
        {
            string whatsappJsonString = JsonSerializer.Serialize(whatsapp);
            logger.LogError("Whatsapp Mesajı: {whatsapp}", whatsappJsonString);
            var profileName = Regex.Replace(whatsapp.Entry[0].Changes[0].Value.Contacts[0].Profile.Name, @"\\u([0-9A-Fa-f]{4})", m => ((char)Convert.ToInt32(m.Groups[1].Value, 16)).ToString());
            var request = new WhatsAppWebhookRequest()
            {
                Type = whatsapp.Entry[0].Changes[0].Field,
                BaseChatId = whatsapp.Entry[0].Changes[0].Value.Metadata.PhoneNumberId,
                CustomerPhone = whatsapp.Entry[0].Changes[0].Value.Contacts[0].WaId,
                From = whatsapp.Entry[0].Changes[0].Value.Messages[0].From,
                To = whatsapp.Entry[0].Changes[0].Value.Messages[0].To,
                MessageId = whatsapp.Entry[0].Changes[0].Value.Messages[0].Id,
                Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(long.Parse(whatsapp.Entry[0].Changes[0].Value.Messages[0].Timestamp)).UtcDateTime,
                ContentType = whatsapp.Entry[0].Changes[0].Value.Messages[0].Type,
                Content = whatsapp.Entry[0].Changes[0].Value.Messages[0].Text?.Body,
                MetaData = new Dictionary<string, object>
                {
                    { "Contacts.Profile.Name", profileName },
                    { "display_phone_number", whatsapp.Entry[0].Changes[0].Value.Metadata.DisplayPhoneNumber },
                    { "phone_number_id", whatsapp.Entry[0].Changes[0].Value.Metadata.PhoneNumberId }
                }
            };
            return await handler.HandleWebhookAsync(request);
        })
        .WithTags("Conversations.Chats")
        .WithGroupName("apiv1")
        .Produces(StatusCodes.Status202Accepted)
        .AllowAnonymous();
    }

}
