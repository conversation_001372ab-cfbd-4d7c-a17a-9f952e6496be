import { Col, Row } from "antd";
import UserListIndex from "./Components/LeftUserList/UserListIndex";
import MessageDetailsIndex from "./Components/RightMessageDetails/MessageDetailsIndex";
import TopOptions from "./Components/TopOptions";
import { useQueryClient } from "react-query";
import { useEffect } from "react";
import endPoints from "./EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const ChatIndex = () => {
  const queryClient = useQueryClient();
  const {selectedChatItem} = useSelector((state: RootState) => state.chat);
  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endPoints.getUserChatListFilter,
      exact: false,
    });
  }, []);
  return (
    <>
      <Col xs={24}
      className="fixed !w-[94.8%] !h-screen"
      >
        <Row className="" >
          <Col xs={24}>
            <TopOptions />
          </Col>
          <Col xs={24} xl={6} >
            <UserListIndex />
          </Col>
          {
            selectedChatItem?<>
            
          <Col xs={24} xl={18}  >
            <MessageDetailsIndex />
          </Col>
            </>:<>
            Bir chat sec
            </>
          }
        </Row>
      </Col>
    </>
  );
};

export default ChatIndex;
