export const determineChatChannel = (type:"icon" |"value",statusId:number)=>{
    if(type==="icon")
    {
        switch(statusId)
        {
          case 1:
            return "!bg-[#21c55e]"
           case 2:
            return "!bg-[#e05b4a]"
           case 3:
            return "!bg-[#ff9a0b]"
          
        }
    }
    else{
        switch(statusId)
        {
          case 1:
            return "WhatsApp"
           case 2:
            return "Telegram"
           case 3:
            return "Facebook"
          
        }
    }

}


export const determineChatStatus = (type:"color" |"value"|"select",statusId:number,t:any)=>{
  if(type==="color")
  {
      switch(statusId)
      {
        case 1:
          return "!bg-[#21c55e]"
         case 2:
          return "!bg-[#e05b4a]"
         case 3:
          return "!bg-[#0096d1]"
        
      }
  }
  else if(type==="select")
  {
    return [
      {label:t("chat.active"),value:1},
      {label:t("chat.closed"),value:2},
      {label:t("chat.archived"),value:3},
    ]
  }
  else{
      switch(statusId)
      {
        case 1:
          return t("chat.active")
         case 2:
          return t("chat.closed")
         case 3:
          return  t("chat.archived")
        
      }
  }

}

export const determineMessageStatus = (type:"color" |"value",statusId:number,t:any)=>{
  if(type==="color")
  {
      switch(statusId)
      {
        case 1:
          return "!bg-[#21c55e]"
         case 2:
          return "!bg-[#e05b4a]"
         case 3:
          return "!bg-[#0096d1]"
        
      }
  }
  else{
      switch(statusId)
      {
        case 1:
          return t("chat.start")
         case 2:
          return t("chat.delivered")
         case 3:
          return  t("chat.read")
        
      }
  }

}


