using MediatR;
using Requests.Domain;
using Shared.Application;

namespace Requests.Application.Tickets.CreateTicket;

public record CreateTicketCommand : IRequest<Result<Guid>>
{
    public TicketType TicketType { get; init; } = TicketType.Ticket;
    public Guid? TopTicketId { get; init; }
    public Guid SubjectId { get; init; }
    public Guid CustomerId { get; init; }
    public Guid? CallId { get; init; }
    public Guid? ChatId { get; init; }
    public required string Title { get; init; }
    public string? Description { get; init; }
    public Guid? NotificationWayId { get; init; }
    public Guid? UserId { get; init; }
    public List<Guid> DepartmentIds { get; init; } = [];
    public List<CreateTicketFileDto> TicketFiles { get; init; } = [];
    public PriorityEnum Priority { get; init; }
    public DateTime? EndDate { get; init; }
    public List<Guid>? Watchlist { get; init; }
    public List<string> Tags { get; init; } = [];
    public Dictionary<string, string>? AttributeData { get; init; }
}

public record CreateTicketFileDto
{
    public Guid FileId { get; init; }
    public required string FileName { get; init; }
    public required string FilePath { get; init; }
}

public record TicketFileDto
{
    public required string FileName { get; init; }
    public required string FilePath { get; init; }
    public long FileSize { get; init; }
    public string? ContentType { get; init; }
}
