@tailwind base;
@tailwind components;
@tailwind utilities;

/* @import url("../src/apps//Dashboard//Style/TabContainer.css"); */

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  --menu-item-height: 39px;
  --table-text-size: 39px;
}

/* #search-place-container .ant-select{
  width: 100% !important;
} */
.small-table-title th {
  font-size: 10px !important;
}

.ant-spin-nested-loading {
  height: 100% !important;
}

.search-emoji {
  display: none !important;
}

.search-box {
  width: 90% !important;
}

.menu-height {
  height: var(--menu-item-height);
  padding: 0.5rem !important;
}

.ant-tree-node-content-wrapper {
  padding-inline: 0px !important;
  display: flex !important;
  width: 100% !important;
}

.ant-tree-node-content-wrapper,
.ant-tree-treenode {
  width: 100% !important;
}

.ant-tree-title {
  display: flex !important;
  width: 100%;
}

/* Add this CSS in your styles.css or within a <style> block in the component */

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(3);
    opacity: 0;
  }
}

.wave {
  animation: wave 2s infinite;
  animation-timing-function: ease-out;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

@keyframes slideIn {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0);
  }
}

.call-container {
  animation: slideIn 0.5s ease-out;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

.ant-form-item {
  margin: 0 !important;
}

.test {
  background-color: blue !important;
}

.ant-tag {
  border-radius: 0 !important;
}

input,
.ant-select-selector,
textarea,
.ant-input-password,
.ant-picker {
  border-radius: 0 !important;
}

:where(.css-dev-only-do-not-override-1a3rktk).ant-form-vertical .ant-form-item:not(.ant-form-item-horizontal) .ant-form-item-label {
  padding: 0 0 1px !important;
}

:where(.css-dev-only-do-not-override-opqoe6).ant-tabs-top>.ant-tabs-nav {
  margin: 10px !important;
}

.ant-form-item-label label {
  font-size: 12px !important;
}

::placeholder {
  font-size: 12px;
}

input,
.ant-select-selection-placeholder {
  font-size: 12px !important;
}

.ant-tabs-tab-btn {
  font-size: 13px;
  color: #111111 !important;
  font-weight: 400;
}

.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: black !important;
  font-weight: bold;
}

.ant-radio-inner {
  width: 13px !important;
  height: 13px !important;
}

.ant-picker-panel-layout {
  min-width: 250px !important;
}

.ant-picker-time-panel-container .ant-picker-panel-layout div {
  width: 100% !important;
}

.ant-btn {
  border-radius: 0px !important;
}

.ant-table-cell-row-hover {
  background-color: #d1d5db !important;
  cursor: pointer;
}

:where(.css-dev-only-do-not-override-opqoe6).ant-table-wrapper .ant-table-column-sorters {
  display: flex;
  flex: auto;
  align-items: center;
  justify-content: start !important;
}

:where(.css-dev-only-do-not-override-opqoe6).ant-table-wrapper .ant-table-column-title {
  flex: none;
}

@keyframes upDown {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes downUp {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(20px);
  }
}

.animate-up {
  animation: upDown 1.8s ease-in-out infinite;
}

.animate-down {
  animation: downUp 1.8s ease-in-out infinite;
}

.delay-0 {
  animation-delay: 0s;
}

.delay-1 {
  animation-delay: 0.3s;
}

.delay-2 {
  animation-delay: 0.6s;
}

.animated-bell {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  display: inline-block;
  color: #ef4444;
  /* Tailwind'in red-500 rengi */
  font-size: 1.125rem;
  /* Tailwind'deki text-lg karşılığı */
  animation: sway 2s ease-in-out infinite, ripple 1.5s ease-out infinite;
  border-radius: 9999px;
  /* dalga efekti düzgün yayılacak */
}

@keyframes sway {

  0%,
  100% {
    transform: translateX(0);
  }

  50% {
    transform: translateX(-5px);
  }
}

@keyframes ripple {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.6);
  }

  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

th {
  font-size: 13px !important;
}

.ant-th {
  position: relative;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  text-align: start;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s ease;
}

.custom-btn-save,
.ant-btn-variant-solid,
.ant-tag-blue {
  background-color: #0096d1 !important;
  color: white !important;
}

.custom-btn-error,
.ant-modal-confirm-btns .ant-btn-variant-outlined {
  background-color: #d1d1d1 !important;
  color: black !important;
}

#app {
  width: 100vw;
  height: 100vh;
}

.react-flow__edge-textbg {
  fill: #f7f9fb;
}

.react-flow {
  /* Custom Variables */
  --xy-theme-selected: #f57dbd;
  --xy-theme-hover: #c5c5c5;
  --xy-theme-edge-hover: black;
  --xy-theme-color-focus: #e8e8e8;

  /* Built-in Variables see https://reactflow.dev/learn/customization/theming */
  --xy-node-border-default: 1px solid #ededed;

  --xy-node-boxshadow-default: 0px 3.54px 4.55px 0px #00000005,
    0px 3.54px 4.55px 0px #0000000d, 0px 0.51px 1.01px 0px #0000001a;

  --xy-node-border-radius-default: 8px;

  --xy-handle-background-color-default: #ffffff;
  --xy-handle-border-color-default: #aaaaaa;

  --xy-edge-label-color-default: #505050;
}

.react-flow.dark {
  --xy-node-boxshadow-default: 0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.05),
    /* light shadow */
    0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.13),
    /* medium shadow */
    0px 0.51px 1.01px 0px rgba(255, 255, 255, 0.2);
  /* smallest shadow */
  --xy-theme-color-focus: #535353;
}

/* Customizing Default Theming */

.react-flow__node {
  box-shadow: var(--xy-node-boxshadow-default);
  border-radius: var(--xy-node-border-radius-default);
  background-color: var(--xy-node-background-color-default);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 10px;
  font-size: 12px;
  flex-direction: column;
  border: var(--xy-node-border-default);
  color: var(--xy-node-color, var(--xy-node-color-default));
}

.react-flow__node.selectable:focus {
  box-shadow: 0px 0px 0px 4px var(--xy-theme-color-focus);
  border-color: #d9d9d9;
}

.react-flow__node.selectable:focus:active {
  box-shadow: var(--xy-node-boxshadow-default);
}

.react-flow__node.selectable:hover,
.react-flow__node.draggable:hover {
  border-color: var(--xy-theme-hover);
}

.react-flow__node.selectable.selected {
  border-color: var(--xy-theme-selected);
  box-shadow: var(--xy-node-boxshadow-default);
}

.react-flow__node-group {
  background-color: rgba(207, 182, 255, 0.4);
  border-color: #9e86ed;
}

.react-flow__edge.selectable:hover .react-flow__edge-path,
.react-flow__edge.selectable.selected .react-flow__edge-path {
  stroke: var(--xy-theme-edge-hover);
}

.react-flow__handle {
  background-color: var(--xy-handle-background-color-default);
}

.react-flow__handle.connectionindicator:hover {
  pointer-events: all;
  border-color: var(--xy-theme-edge-hover);
  background-color: white;
}

.react-flow__handle.connectionindicator:focus,
.react-flow__handle.connectingfrom,
.react-flow__handle.connectingto {
  border-color: var(--xy-theme-edge-hover);
}

.react-flow__node-resizer {
  border-radius: 0;
  border: none;
}

.react-flow__resize-control.handle {
  background-color: #ffffff;
  border-color: #9e86ed;
  border-radius: 0;
  width: 5px;
  height: 5px;
}

.ant-table-cell {
  padding: 0 !important;
}

.ant-pagination-total-text,
.ant-select-selection-item,
.ant-pagination-item,
.ant-form-item-explain-error,
.ant-scroll-number-only-unit {
  font-size: 11px !important;
}

.ant-pagination-item {
  width: 20px !important;
  height: 20px !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.ant-pagination {
  align-items: center;
}

.custom-filter-tabs .ant-tabs-tab {
  padding: 0px !important;
  margin-bottom: 0px !important;
}

.custom-filter-tabs .ant-tabs-nav,
.custom-filter-tabs {
  padding: 0px !important;
  margin-bottom: 0px !important;
  margin: 5px 0px !important;
  height: auto !important;
}

.custom-filter-tabs .tab-text {
  font-weight: normal !important;
}

.custom-filter-tabs .ant-tabs-content-holder {
  display: none !important;
}

.custom-filter-tabs .ant-tabs-tab-active {
  font-weight: normal !important;
  background-color: #fafafa !important;
  border-color: #d9d9d9 !important;
}

.no-active-tab-quick .ant-tabs-ink-bar {
  display: none !important;
}

.no-active-tab-quick .ant-tabs-tab-active,
.no-active-tab-quick .ant-tabs-tab-active .tab-text {
  color: inherit !important;
  border-color: transparent !important;
  font-weight: normal !important;
  text-shadow: none !important;
}

.no-active-tab-quick .ant-tabs-tab {
  font-weight: normal !important;
}

.ant-upload-drag {
  height: auto !important;
}

/* .ticket-form .ant-form-item-row {
  display: flex;
  flex-direction: row !important;
} */

.ticket-form-attach .ant-select-selector {
  background-color: transparent !important;
}

.ticket-form-attach .ant-picker {
  background-color: transparent !important;
}

/* .ticket-form-attach .ant-select-selector:nth-child(0) {
  background-color: white !important;
} */

.ticket-detail-tabs .ant-card {
  min-height: auto !important;
  border: none !important;
  border-radius: 0px !important;
}