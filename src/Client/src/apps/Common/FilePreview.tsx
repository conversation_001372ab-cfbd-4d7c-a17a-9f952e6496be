import React from 'react';
import { Image, Typography } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";

const { Text } = Typography;

// Dosya tipi tanımı
export interface FileItem {
    FileId: string;
    FileName: string;
    FilePath: string;
    MimeType?: string;
    FileSizeInBytes?: number;
}

interface FilePreviewProps {
    files: FileItem[];
    title?: string;
    showCount?: boolean;
    imageSize?: {
        width: number;
        height: number;
    };
    className?: string;
    style?: React.CSSProperties;
    showTitle?: boolean;
    maxWidth?: string;
    gap?: string;
    showFileNames?: boolean;
    onFileClick?: (file: FileItem) => void;
}

const FilePreview: React.FC<FilePreviewProps> = ({
    files = [],
    title = "Dosyalar",
    showCount = true,
    imageSize = { width: 60, height: 60 },
    className = "",
    style = {},
    showTitle = true,
    maxWidth = "450px",
    gap = "6px",
    showFileNames = true,
    onFileClick
}) => {

    // Dosya tipine göre ikon döndüren fonksiyon
    const getFileIcon = (mimeType: string) => {
        if (mimeType?.startsWith('image/')) {
            return '🖼️';
        } else if (mimeType?.includes('pdf')) {
            return '📄';
        } else if (mimeType?.includes('word')) {
            return '📝';
        } else if (mimeType?.includes('excel') || mimeType?.includes('spreadsheet')) {
            return '📊';
        } else if (mimeType?.includes('video/')) {
            return '🎥';
        } else if (mimeType?.includes('audio/')) {
            return '🎵';
        } else if (mimeType?.includes('zip') || mimeType?.includes('rar')) {
            return '📦';
        } else {
            return '📎';
        }
    };

    // Görsel dosya olup olmadığını kontrol eden fonksiyon
    const isImageFile = (mimeType: string) => {
        return mimeType?.startsWith('image/');
    };

    // Dosya boyutunu formatlamak için yardımcı fonksiyon
    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    // Default dosya click handler
    const handleFileClick = (file: FileItem) => {
        if (onFileClick) {
            onFileClick(file);
        } else {
            // Default davranış: dosyayı yeni sekmede aç
            window.open(`${setBackEndUrl()}/Uploads/${file.FilePath}`, '_blank');
        }
    };

    // Dosya yoksa render etme
    if (!files || files.length === 0) {
        return null;
    }

    return (
        <>
            {/* CSS for hover effects */}
            <style>
                {`
          .file-preview-item {
            transition: all 0.2s ease;
          }
          
          .file-preview-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          /* Custom scrollbar for horizontal scroll */
          .file-preview-container::-webkit-scrollbar {
            height: 6px;
          }

          .file-preview-container::-webkit-scrollbar-track {
            background: #f3f4f6;
            border-radius: 3px;
          }

          .file-preview-container::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
          }

          .file-preview-container::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
          }
        `}
            </style>

            <div className={className} style={style}>
                {/* Title */}
                {showTitle && (
                    <div style={{
                        fontSize: '11px',
                        color: '#666',
                        marginBottom: '6px',
                        fontWeight: 500
                    }}>
                        {title}
                        {showCount && (
                            <span style={{ color: '#64748b', fontWeight: 'normal' }}>
                                {' '}({files.length})
                            </span>
                        )}
                    </div>
                )}

                {/* File preview container */}
                <div
                    className="file-preview-container"
                    style={{
                        display: 'flex',
                        gap: gap,
                        overflowX: 'auto',
                        padding: '2px 0',
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#d1d5db #f3f4f6',
                        maxWidth: maxWidth
                    }}
                >
                    <Image.PreviewGroup>
                        {files.map((file, index) => (
                            <div
                                key={file.FileId || index}
                                className="file-preview-item"
                                style={{
                                    position: 'relative',
                                    width: `${imageSize.width}px`,
                                    height: `${imageSize.height}px`,
                                    borderRadius: '8px',
                                    overflow: 'hidden',
                                    flexShrink: 0,
                                    border: '1px solid #e5e7eb',
                                    cursor: 'pointer'
                                }}
                            >
                                {isImageFile(file.MimeType || '') ? (
                                    // Görsel dosya
                                    <Image
                                        width="100%"
                                        height="100%"
                                        src={`${setBackEndUrl()}/Uploads/${file.FilePath}`}
                                        style={{
                                            objectFit: 'cover',
                                            cursor: 'pointer'
                                        }}
                                        preview={{
                                            src: `${setBackEndUrl()}/Uploads/${file.FilePath}`,
                                            mask: (
                                                <div style={{
                                                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    color: 'white',
                                                    fontSize: '12px'
                                                }}>
                                                    <EyeOutlined />
                                                </div>
                                            )
                                        }}
                                    />
                                ) : (
                                    // Görsel olmayan dosya
                                    <div
                                        style={{
                                            width: '100%',
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            backgroundColor: '#f8fafc',
                                            cursor: 'pointer'
                                        }}
                                        onClick={() => handleFileClick(file)}
                                        title={`${file.FileName}${file.FileSizeInBytes ? ` (${formatFileSize(file.FileSizeInBytes)})` : ''}`}
                                    >
                                        <div style={{ fontSize: '20px', marginBottom: '4px' }}>
                                            {getFileIcon(file.MimeType || '')}
                                        </div>
                                        <div style={{
                                            fontSize: '8px',
                                            color: '#64748b',
                                            textAlign: 'center',
                                            padding: '0 2px'
                                        }}>
                                            {file.FileName.split('.').pop()?.toUpperCase()}
                                        </div>
                                    </div>
                                )}

                                {/* Dosya adı tooltip */}
                                {showFileNames && (
                                    <div
                                        style={{
                                            position: 'absolute',
                                            bottom: '0',
                                            left: '0',
                                            right: '0',
                                            backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                            color: 'white',
                                            padding: '4px 6px',
                                            fontSize: '9px',
                                            textAlign: 'center',
                                            whiteSpace: 'nowrap',
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis'
                                        }}
                                        title={file.FileName}
                                    >
                                        {file.FileName}
                                    </div>
                                )}
                            </div>
                        ))}
                    </Image.PreviewGroup>
                </div>
            </div>
        </>
    );
};

export default FilePreview;