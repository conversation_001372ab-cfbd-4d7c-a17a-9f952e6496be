import { RootState } from "@/store/Reducers";
import { CloseOutlined } from "@ant-design/icons";
import { Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { handleSetSelectedChatMessageItem } from "../../ClientSideStates";

const ReplyMessage = () => {
    const{Text} = Typography
    const{selectedMessageItem} = useSelector((state:RootState)=>state.chat)
    const dispatch = useDispatch()
    return ( <>
    
    <div className="!py-2 rounded-md border-l-4 border-green-600 bg-[#58666e] text-white text-sm relative !w-[50%] !mb-1">
          <div className="flex justify-between ">
            <div className="!p-2">
                <Text className="!text-white" >{selectedMessageItem?.Content} </Text>
            </div>
            <CloseOutlined className="!text-white !m-2 cursor-pointer" onClick={()=>dispatch(handleSetSelectedChatMessageItem({data:null}))} />
          </div>
          
        </div>
    </> );
}
 
export default ReplyMessage;