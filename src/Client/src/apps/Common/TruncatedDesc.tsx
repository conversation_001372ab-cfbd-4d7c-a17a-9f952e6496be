// ExpandableText.tsx
import React, { useState } from "react";
import { Modal, Button } from "antd";
import { EyeFilled } from "@ant-design/icons";

interface ExpandableTextProps {
  text: string;
  limit?: number;
  title?: string;
  textClassName?:string
}

const ExpandableText: React.FC<ExpandableTextProps> = ({
  text,
  limit = 100,
  title,
  textClassName
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);

  const showText = text.length > limit ? text.slice(0, limit) + "..." : text;
  const isTruncated = text.length > limit;

  return (
    <div className="!flex !max-h-[500px] !overflow-scroll">
      <span className={textClassName||"!text-xs !text-gray-500"}>{showText}</span>
      {isTruncated && (
        <EyeFilled
          className="!text-[#0096d1]"
          onClick={() => setIsModalVisible(true)}
        />
      )}

      <Modal
        title={title}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
      >
        <p>{text}</p>
      </Modal>
    </div>
  );
};

export default ExpandableText;
