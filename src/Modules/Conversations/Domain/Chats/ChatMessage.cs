using Shared.Domain;

namespace Conversations.Domain.Chats;

public class ChatMessage(
    Guid chatId,
    string content,
    MessageDirection direction,
    string senderId,
    MessageContentType contentType,
    Guid? replyToMessageId = null) : AuditableEntity
{
    public Guid Id { get; set; }
    public Guid ChatId { get; private set; } = chatId;
    public string? ExternalId { get; private set; }
    public Guid? ReplyToMessageId { get; private set; } = replyToMessageId;
    public MessageDirection Direction { get; private set; } = direction;
    public string Content { get; private set; } = content;
    public MessageContentType ContentType { get; private set; } = contentType;
    public MessageStatus Status { get; private set; } = MessageStatus.Sent;
    public DateTime SentAt { get; private set; } = DateTime.Now;
    public DateTime? DeliveredAt { get; private set; }
    public DateTime? ReadAt { get; private set; }
    public string SenderId { get; private set; } = senderId;
    public SenderType SenderType { get; private set; } = direction == MessageDirection.Incoming
        ? SenderType.Customer
        : SenderType.User;
    public string? MetaData { get; private set; }
    private readonly List<ChatAttachment> _attachments = [];
    public IReadOnlyCollection<ChatAttachment> Attachments => _attachments.AsReadOnly();

    public void SetExternalId(string externalId)
    {
        ExternalId = externalId;
    }

    public void MarkAsDelivered()
    {
        Status = MessageStatus.Delivered;
        DeliveredAt = DateTime.Now;
        Raise(new MessageDeliveredEvent(Id));
    }

    public void MarkAsRead()
    {
        Status = MessageStatus.Read;
        ReadAt = DateTime.Now;
        Raise(new MessageReadEvent(Id));
    }

    public void SetMetaData(string metaData)
    {
        MetaData = metaData;
    }

    public void AddAttachment(string url, AttachmentType type, string? fileName = null, string? contentType = null, long? fileSize = null)
    {
        var attachment = new ChatAttachment(Id, url, type, fileName, contentType, fileSize);
        _attachments.Add(attachment);
        Raise(new AttachmentAddedEvent(Id, attachment.Id));
    }
}
