using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Requests.Domain;

namespace Requests.Infrastructure.Data.Configurations;

public class TicketCommentConfiguration : IEntityTypeConfiguration<TicketComment>
{
    public void Configure(EntityTypeBuilder<TicketComment> builder)
    {
        builder.ToTable("TicketComments");

        builder.<PERSON><PERSON><PERSON>(t => t.Id);

        builder.Property(t => t.Comment)
            .IsRequired();

        builder.HasOne<Ticket>()
            .WithMany()
            .HasForeignKey(t => t.TicketId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(c => c.Files)
            .WithOne()
            .HasForeignKey(f => f.CommentId)
            .OnDelete(DeleteBehavior.NoAction);
    }
}
