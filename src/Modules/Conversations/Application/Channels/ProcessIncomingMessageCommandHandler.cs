using Conversations.Application.Abstractions;
using Conversations.Application.Chats.CreateChat;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.Channels;

public class ProcessIncomingMessageCommandHandler(
    IConversationDbContext dbContext,
    IMediator mediator
) : IRequestHandler<ProcessIncomingMessageCommand, Result<Guid>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IMediator _mediator = mediator;

    public async Task<Result<Guid>> Handle(ProcessIncomingMessageCommand request, CancellationToken cancellationToken)
    {
        // Check if the message already exists
        var existingMessage = await _dbContext.ChatMessage
            .FirstOrDefaultAsync(m => m.ExternalId == request.ExternalMessageId, cancellationToken);

        if (existingMessage != null)
        {
            return Result.Success(existingMessage.Id);
        }

        // Find or create the chat
        var chat = await _dbContext.Chat
            .FirstOrDefaultAsync(c =>
                c.ExternalId == request.ExternalChatId &&
                c.Channel == request.Channel &&
                c.Status == ChatStatus.Active,
                cancellationToken);

        if (chat == null)
        {
            // Create a new chat
            var createChatResult = await _mediator.Send(new CreateChatCommand
            {
                CustomerId = request.CustomerId,
                CustomerName = request.CustomerName,
                Channel = request.Channel,
                ExternalId = request.ExternalChatId,
                BaseChatId = request.BaseChatId,
            }, cancellationToken);

            if (!createChatResult.IsSuccess)
            {
                return Result.Failure<Guid>(createChatResult.Error);
            }

            chat = await _dbContext.Chat
                .FirstAsync(c => c.Id == createChatResult.Value, cancellationToken);
        }

        // Add the message to the chat
        var message = chat.AddMessage(
            request.Content,
            MessageDirection.Incoming,
            request.Sender,
            request.ContentType);

        message.SetExternalId(request.ExternalMessageId);

        if (!string.IsNullOrEmpty(request.MetaData))
        {
            message.SetMetaData(request.MetaData);
        }

        // Add attachments if any
        if (request.Attachments != null && request.Attachments.Any())
        {
            foreach (var attachment in request.Attachments)
            {
                message.AddAttachment(
                    attachment.Url,
                    attachment.Type,
                    attachment.FileName,
                    attachment.ContentType,
                    attachment.FileSize);
            }
        }

        await _dbContext.SaveChangesAsync(cancellationToken);

        return Result.Success(message.Id);
    }
}
