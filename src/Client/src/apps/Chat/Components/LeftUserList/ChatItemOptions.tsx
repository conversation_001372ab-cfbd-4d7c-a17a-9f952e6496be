import { determineChatStatus } from "@/helpers/Chat";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { EllipsisOutlined } from "@ant-design/icons";
import { Dropdown } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";

const ChatItemOptions: FC<{ item: any }> = ({ item }) => {
    const queryClient = useQueryClient()
  const { t } = useTranslation();
  const {userChatFilter:filter} = useSelector((state:RootState)=>state.chat)

  const items = determineChatStatus("select", item.StatusId, t)
  ?.filter((status: any) => {
    if (filter?.Status === 1) {
      return status.value === 2; // sadece "closed"
    } else if (filter?.Status === 2) {
      return status.value === 3; // sadece "archived"
    }
    return true; // hepsi
  })
  .map((status: any) => ({
    key: status.value,
    label: status.label,
  }));

  const handleChangeStatus = (value: number) => {
    try {
        openNotificationWithIcon("success", t("form.transactionSuccessful"));
        queryClient.resetQueries({
            queryKey: endPoints.getUserChatListFilter,
            exact: false,
          });
    } catch (error) {
        showErrorCatching(error,null,false,t)
    }
  };

  return (
    <>
    {
        filter?.Status!==3&&(
            <Dropdown menu={{ items }} trigger={["click"]} placement="bottomRight">
            <EllipsisOutlined className="cursor-pointer text-lg" />
          </Dropdown>
        )
    }
      
    </>
  );
};

export default ChatItemOptions;
