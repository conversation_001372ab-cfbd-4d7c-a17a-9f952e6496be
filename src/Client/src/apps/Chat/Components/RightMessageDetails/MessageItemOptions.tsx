import { FC } from "react";
import { useDispatch } from "react-redux";
import { Dropdown, MenuProps, message } from "antd";
import { CopyOutlined, DownOutlined, MoreOutlined, SyncOutlined } from "@ant-design/icons";
import { handleSetSelectedChatMessageItem } from "../../ClientSideStates";

const MessageItemOptions: FC<{ item: any }> = ({ item }) => {
  const dispatch = useDispatch();

  const handleSelectMessage = () => {
    dispatch(handleSetSelectedChatMessageItem({ data: item }));
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(item.Content);
      message?.success("Mesajinikopyalandı.");
    } catch (error) {
      console.error("Kopyalama başarısız:", error);
    }
  };

  const items: MenuProps["items"] = [
    {
      key: "reply",
      icon:<SyncOutlined/>,
      label: "<PERSON><PERSON><PERSON><PERSON>",
      onClick: handleSelectMessage,
    },
    {
      key: "copy",
      icon:<CopyOutlined/>,
      label: "Kopyala",
      onClick: handleCopyToClipboard,
    },
  ];

  return (
    <Dropdown menu={{ items }} trigger={["click"]}>
      <MoreOutlined className="text-black !text-base  cursor-pointer" />
    </Dropdown>
  );
};

export default MessageItemOptions;
