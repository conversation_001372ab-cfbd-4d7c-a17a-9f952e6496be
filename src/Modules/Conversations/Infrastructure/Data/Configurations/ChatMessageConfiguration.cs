using Conversations.Domain.Chats;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Conversations.Infrastructure.Data.Configurations;

public class ChatMessageConfiguration : IEntityTypeConfiguration<ChatMessage>
{
    public void Configure(EntityTypeBuilder<ChatMessage> builder)
    {
        builder.ToTable("ChatMessages", "Conversations");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.ChatId).IsRequired();
        builder.Property(x => x.ExternalId).HasMaxLength(100);
        builder.Property(x => x.ReplyToMessageId);
        builder.Property(x => x.Direction).IsRequired();
        builder.Property(x => x.Content).IsRequired();
        builder.Property(x => x.ContentType).IsRequired();
        builder.Property(x => x.Status).IsRequired();
        builder.Property(x => x.SentAt).IsRequired();
        builder.Property(x => x.DeliveredAt);
        builder.Property(x => x.ReadAt);
        builder.Property(x => x.SenderId).IsRequired().HasMaxLength(100);
        builder.Property(x => x.SenderType).IsRequired();
        builder.Property(x => x.MetaData);

        builder.HasIndex(x => x.ExternalId).IsUnique();
        builder.HasIndex(x => x.ChatId);
        builder.HasIndex(x => x.ReplyToMessageId);
        builder.HasIndex(x => x.Status);
        builder.HasIndex(x => x.SentAt);

        // Self-referencing relationship for reply messages
        builder.HasOne<ChatMessage>()
            .WithMany()
            .HasForeignKey(x => x.ReplyToMessageId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasMany(x => x.Attachments)
            .WithOne()
            .HasForeignKey(x => x.MessageId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
