import { determineChatStatus } from "@/helpers/Chat";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { EllipsisOutlined } from "@ant-design/icons";
import { Dropdown, Modal } from "antd";
import { FC } from "react";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { changeChatStatusToArchived, changeChatStatusToClosed } from "../../Services";

const ChatItemOptions: FC<{ item: any }> = ({ item }) => {
    const queryClient = useQueryClient()
  const { t } = useTranslation();
  const {userChatFilter:filter} = useSelector((state:RootState)=>state.chat)

  const items = determineChatStatus("select", item.StatusId, t)
  ?.filter((status: any) => {
    if (filter?.status === 1) {
      return status.value === 2; 
    } else if (filter?.status === 2) {
      return status.value === 3; 
    }
    return true; 
  })
  .map((status: any) => ({
    key: status.value,
    label: status.label,
    onClick: () => {
      confirm(status.value)
    },
  }));

  const handleChangeStatus = async(value: number) => {
    try {
      value==2 ? await changeChatStatusToClosed(item) : await changeChatStatusToArchived(item)
        openNotificationWithIcon("success", t("form.transactionSuccessful"));
        queryClient.resetQueries({
            queryKey: endPoints.getUserChatListFilter,
            exact: false,
          });
    } catch (error) {
        showErrorCatching(error,null,false,t)
    }
  };

  const confirm = (value:number) => {
    Modal.confirm({
      title: t("autoDialer.list.warning"),
      icon: null,
      content: t("autoDialer.list.deleteModalDesc"),
      okText: t("autoDialer.list.delete"),
      cancelText: t("autoDialer.list.cancel"),
      onOk: async () => {
        handleChangeStatus(value)
      },
    });
  };

  return (
    <>
    {
        filter?.Status!==3&&(
            <Dropdown menu={{ items }} trigger={["click"]} placement="bottomRight">
            <EllipsisOutlined className="cursor-pointer text-lg" />
          </Dropdown>
        )
    }
      
    </>
  );
};

export default ChatItemOptions;
