using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Tasks.Application.Comments.ListComments;

public class ListCommentsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/tasks/tasks/{taskId}/comments", async (
            Guid taskId,
            string? sortProperty,
            string? sortType,
            ISender sender,
            CancellationToken cancellationToken) =>
        {
            var query = new ListCommentsQuery(taskId, sortProperty ?? "InsertDate", sortType ?? "desc");
            var result = await sender.Send(query, cancellationToken);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Tasks.Comments")
        .WithGroupName("apiv1")
        .Produces<Result<List<CommentDto>>>(StatusCodes.Status200OK)
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .RequireAuthorization("Tasks.Comments");
    }
}
