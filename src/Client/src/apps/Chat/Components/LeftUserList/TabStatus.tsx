import { RootState } from "@/store/Reducers";
import { Tabs, TabsProps } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetUserChatFilter } from "../../ClientSideStates";

const TabStatus = () => {
    const {userChatFilter:filter} = useSelector((state:RootState)=>state.chat)
    const dispatch = useDispatch()
    const onChange = (key: string) => {
        const currentFilter = {...filter,status:Number(key)}
       dispatch(hanldleSetUserChatFilter({ filter:currentFilter }));
       
    }
      const items: TabsProps['items'] = [
        {
          key: "1",
          label: "Open",
          
        },
        {
          key: "2",
          label: 'Closed',
         
        },
        {
          key: "3",
          label: 'Archived',
          
        },
      ];

    return ( <>
    <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    
    </> );
}
 
export default TabStatus;