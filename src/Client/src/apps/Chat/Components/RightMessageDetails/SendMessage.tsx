import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { SendOutlined } from "@ant-design/icons";
import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import endPoints from "../../EndPoints";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { replayMessage, sendMessage } from "../../Services";
import {
  HubConnectionBuilder,
  LogLevel,
  HttpTransportType,
  HubConnection,
} from "@microsoft/signalr";
import { useEffect, useRef, useState } from "react";
import { setBackEndUrl } from "@/helpers/SetBackEndUrl";

const SendMessage = () => {
  const [connection, setConnection] = useState<HubConnection | null>(null);
  const [form] = Form.useForm();
  const { selectedChatItem,selectedMessageItem } = useSelector((state: RootState) => state.chat);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const typingStartedRef = useRef(false); // Şu anda typingStarted mı?

  const chatId = selectedChatItem?.Id;

  const handleOnFinish = async () => {
    const formValues = form.getFieldsValue();
    if(!formValues["Content"])
    {
      return false;
    }
    formValues["ChatId"] = chatId;
    formValues["Direction"] = 2;
    formValues["SenderId"] = userInfoes?.Id;
    formValues["ContentType"] = 1;
    if(selectedMessageItem)
    {
      formValues["ReplyToMessageId"] = selectedMessageItem.Id;
    }
   

    try {
      
      if (typingStartedRef.current && connection && chatId) {
        await connection.invoke("TypingStopped", chatId);
        typingStartedRef.current = false;
      }

      selectedMessageItem?await replayMessage(formValues):await sendMessage(formValues);
      queryClient.resetQueries({
        queryKey: endPoints.getChatMessageDetails,
        exact: false,
      });
      form.resetFields();
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  };

  useEffect(() => {
    const newConnection = new HubConnectionBuilder()
      .withUrl(`${setBackEndUrl()}/hubs/chathub`, {
        accessTokenFactory: () => localStorage.getItem("access_token") || "",
        skipNegotiation: true,
        transport: HttpTransportType.WebSockets,
        withCredentials: true,
      })
      .configureLogging(LogLevel.Information)
      .withAutomaticReconnect()
      .build();

    newConnection
      .start()
      .then(() => {
        console.log("SignalR bağlantısı kuruldu");
        setConnection(newConnection);
      })
      .catch((err) => console.error("SignalR bağlantı hatası:", err));

    return () => {
      if (newConnection) newConnection.stop();
    };
  }, []);

  const handleFocus = async () => {
    const content = form.getFieldValue("Content");
    if (connection && chatId && content?.trim()) {
      await connection.invoke("TypingStarted", chatId);
      typingStartedRef.current = true;
    }
  };

  const handleBlur = async () => {
    if (connection && chatId && typingStartedRef.current) {
      await connection.invoke("TypingStopped", chatId);
      typingStartedRef.current = false;
    }
  };

  const handleChange = async (e: any) => {
    const value = e.target.value;
    const isEmpty = !value?.trim();

    if (connection && chatId) {
      if (!isEmpty && !typingStartedRef.current) {
      
        await connection.invoke("TypingStarted", chatId);
        typingStartedRef.current = true;
      }

      if (isEmpty && typingStartedRef.current) {
    
        await connection.invoke("TypingStopped", chatId);
        typingStartedRef.current = false;
      }
    }
  };

  return (
    <MazakaForm
      form={form}
      onFinish={handleOnFinish}
      submitButtonVisible={false}
    >
      <MazakaTextArea
        autoSize
        label=""
        name="Content"
    
       
       
        placeholder="Say Hello"
        className="!mb-2"
        bordered={false}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onChange={handleChange}
      />

      <MazakaButton htmlType="submit" icon={<SendOutlined />} status="save">
        Send
      </MazakaButton>
    </MazakaForm>
  );
};

export default SendMessage;
