import React, { useState, useRef, useCallback, useEffect } from 'react';
import { GoogleM<PERSON>, Marker, useJsApi<PERSON>oader, Autocomplete } from "@react-google-maps/api";
import { Button, Input, Checkbox, Modal } from 'antd';
import { DeleteOutlined, SearchOutlined, EnvironmentOutlined } from '@ant-design/icons';

const GOOGLE_MAP_LIBRARIES = ["places"];

interface SimpleMapComponentProps {
    marker?: { lat: number; lng: number } | null;
    address?: string;
    onMarkerChange?: (coords: { lat: number; lng: number } | null, address: string) => void;
    onAddressChange?: (address: string) => void;
    visible?: boolean; // Yeni prop
    showInlineMap?: boolean; // Inline harita gösterimi için
}

const SimpleMapComponent: React.FC<SimpleMapComponentProps> = ({
    marker: externalMarker,
    address: externalAddress = '',
    onMarkerChange,
    onAddressChange,
    visible = true,
    showInlineMap = true, // Default true - direkt harita göster
}) => {
    const [internalMarker, setInternalMarker] = useState<{ lat: number; lng: number } | null>(externalMarker || null);
    const [internalAddress, setInternalAddress] = useState(externalAddress);
    const [showMapPopup, setShowMapPopup] = useState(false);
    const [enableMapSelection, setEnableMapSelection] = useState(false);

    const geocoderRef = useRef<google.maps.Geocoder | null>(null);
    const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
    const inputRef = useRef<HTMLInputElement | null>(null);
    const mapRef = useRef<google.maps.Map | null>(null);

    const { isLoaded } = useJsApiLoader({
        id: "google-map-script",
        googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAP_API || "",
        libraries: GOOGLE_MAP_LIBRARIES,
    });

    const defaultCenter = { lat: 38.733509, lng: 35.485397 };

    // visible false ise component'i render etme
    if (!visible) {
        return null;
    }

    // External props değiştiğinde internal state'i güncelle
    useEffect(() => {
        console.log('🗺️ External marker changed:', externalMarker);
        setInternalMarker(externalMarker || null);
    }, [externalMarker]);

    useEffect(() => {
        console.log('📍 External address changed:', externalAddress);
        setInternalAddress(externalAddress || '');
    }, [externalAddress]);

    // Internal marker değişimini logla
    useEffect(() => {
        console.log('📌 Internal marker changed:', internalMarker);
    }, [internalMarker]);

    // Geocoder'ı initialize et
    useEffect(() => {
        if (isLoaded && !geocoderRef.current) {
            geocoderRef.current = new google.maps.Geocoder();
            console.log('🌍 Geocoder initialized');
        }
    }, [isLoaded]);

    // Modal açıldığında haritayı yeniden boyutlandır
    useEffect(() => {
        if (showMapPopup && mapRef.current && isLoaded) {
            setTimeout(() => {
                google.maps.event.trigger(mapRef.current, 'resize');
                if (internalMarker) {
                    mapRef.current?.setCenter(internalMarker);
                }
            }, 100);
        }
    }, [showMapPopup, isLoaded, internalMarker]);

    const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
        const latLng = event.latLng;
        if (!latLng) return;

        const coordinates = {
            lat: latLng.lat(),
            lng: latLng.lng(),
        };

        console.log('🖱️ Map clicked at:', coordinates);
        setInternalMarker(coordinates);

        if (geocoderRef.current) {
            geocoderRef.current.geocode({ location: latLng }, (results, status) => {
                if (status === "OK" && results && results[0]) {
                    const formattedAddress = results[0].formatted_address;
                    console.log('📍 Geocoded address:', formattedAddress);
                    setInternalAddress(formattedAddress);
                    onMarkerChange?.(coordinates, formattedAddress);
                    onAddressChange?.(formattedAddress);
                } else {
                    const coordAddress = `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
                    console.log('📍 Using coordinates as address:', coordAddress);
                    setInternalAddress(coordAddress);
                    onMarkerChange?.(coordinates, coordAddress);
                    onAddressChange?.(coordAddress);
                }
            });
        } else {
            const coordAddress = `${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
            setInternalAddress(coordAddress);
            onMarkerChange?.(coordinates, coordAddress);
            onAddressChange?.(coordAddress);
        }
    }, [onMarkerChange, onAddressChange]);

    const clearMarker = () => {
        console.log('🗑️ Clearing marker');
        setInternalMarker(null);
        setInternalAddress('');
        onMarkerChange?.(null, '');
        onAddressChange?.('');
    };

    const handlePlaceChanged = () => {
        if (autocompleteRef.current) {
            const place = autocompleteRef.current.getPlace();
            if (!place.geometry || !place.geometry.location) return;

            const coordinates = {
                lat: place.geometry.location.lat(),
                lng: place.geometry.location.lng(),
            };

            const address = place.formatted_address || '';
            console.log('🔍 Place selected:', coordinates, address);
            setInternalMarker(coordinates);
            setInternalAddress(address);
            onMarkerChange?.(coordinates, address);
            onAddressChange?.(address);
        }
    };

    const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setInternalAddress(value);
        onAddressChange?.(value);
    };

    const handleCheckboxChange = (e: any) => {
        setEnableMapSelection(e.target.checked);
        if (e.target.checked) {
            setShowMapPopup(true);
        }
    };

    const handleModalClose = () => {
        setShowMapPopup(false);
        setEnableMapSelection(false);
    };

    const handleModalOk = () => {
        setShowMapPopup(false);
        setEnableMapSelection(false);
    };

    const mapContainerStyle = {
        width: "100%",
        height: showInlineMap ? "300px" : "400px",
        borderRadius: "8px"
    };

    const markerIconStyle = React.useMemo(() => {
        if (!isLoaded) return null;
        return {
            path: google.maps.SymbolPath.BACKWARD_CLOSED_ARROW,
            scale: 8,
            fillColor: "#ef4444",
            fillOpacity: 1,
            strokeWeight: 3,
            strokeColor: "#ffffff",
            strokeOpacity: 1,
        };
    }, [isLoaded]);

    if (!isLoaded) {
        return (
            <div className="max-w-2xl mx-auto p-0">
                <div className="bg-white rounded-2xl shadow-sm border border-slate-200 p-8">
                    <div className="flex items-center justify-center h-20">
                        <div className="text-center">
                            <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-100">
                                <svg className="w-8 h-8 text-slate-400 animate-spin" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                            <p className="text-slate-500 font-medium">Google Maps yükleniyor...</p>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="w-full">
            {/* Arama Input ve Checkbox */}
            <div className="bg-white border rounded-lg p-4 mb-4">
                <div className="flex items-center gap-4 mb-3">
                    <div className="flex-1">
                        <Autocomplete
                            onLoad={(autocomplete) => {
                                autocompleteRef.current = autocomplete;
                            }}
                            onPlaceChanged={handlePlaceChanged}
                        >
                            <input
                                type="text"
                                ref={inputRef}
                                placeholder="Adres arayın"
                                value={internalAddress}
                                onChange={handleAddressInputChange}
                                className="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </Autocomplete>
                    </div>
                    {internalMarker && (
                        <Button
                            size="small"
                            danger
                            type="text"
                            icon={<DeleteOutlined />}
                            onClick={clearMarker}
                            title="Konumu temizle"
                        />
                    )}
                </div>

                <div className="flex items-center gap-2">
                    <Checkbox
                        checked={enableMapSelection}
                        onChange={handleCheckboxChange}
                    >
                        Harita üzerinden konum seç (Modal)
                    </Checkbox>
                    <EnvironmentOutlined className="text-gray-400" />
                </div>
            </div>

            {/* Marker bilgisi */}
            {internalMarker && (
                <div className="bg-blue-50 border border-blue-200 p-3 mb-2 mt-2">
                    <div className="flex items-center gap-2">
                        <EnvironmentOutlined className="text-blue-600" />
                        <span className="text-sm text-blue-800">
                            Seçili Konum: {internalAddress || `${internalMarker.lat.toFixed(6)}, ${internalMarker.lng.toFixed(6)}`}
                        </span>
                    </div>
                </div>
            )}

            {/* Inline Harita - Yeni eklenen */}
            {showInlineMap && isLoaded && (
                <div className="border border-gray-300 rounded-lg overflow-hidden mb-4">
                    <GoogleMap
                        mapContainerStyle={mapContainerStyle}
                        center={internalMarker || defaultCenter}
                        zoom={internalMarker ? 16 : 13}
                        onClick={handleMapClick}
                        onLoad={(map) => {
                            mapRef.current = map;
                            console.log('🗺️ Inline map loaded');
                        }}
                        options={{
                            streetViewControl: false,
                            mapTypeControl: false,
                            fullscreenControl: false,
                            zoomControl: true,
                            gestureHandling: 'greedy',
                            styles: [
                                { featureType: "poi", elementType: "labels", stylers: [{ visibility: "off" }] },
                                { featureType: "transit", elementType: "labels", stylers: [{ visibility: "off" }] }
                            ]
                        }}
                    >
                        {internalMarker && (
                            <Marker
                                position={internalMarker}
                                icon={markerIconStyle}
                                animation={google.maps.Animation.DROP}
                                title="Seçili Konum"
                            />
                        )}
                    </GoogleMap>
                </div>
            )}

            {/* Harita Modal */}
            <Modal
                title="Harita Üzerinden Konum Seçin"
                open={showMapPopup}
                onOk={handleModalOk}
                onCancel={handleModalClose}
                width={800}
                footer={[
                    <Button key="cancel" onClick={handleModalClose}>
                        İptal
                    </Button>,
                    <Button key="ok" type="primary" onClick={handleModalOk}>
                        Tamam
                    </Button>
                ]}
            >
                <div className="p-0">
                    <div className="border border-gray-300 overflow-hidden">
                        <GoogleMap
                            mapContainerStyle={{ width: "100%", height: "400px", borderRadius: "8px" }}
                            center={internalMarker || defaultCenter}
                            zoom={internalMarker ? 16 : 13}
                            onClick={handleMapClick}
                            onLoad={(map) => {
                                mapRef.current = map;
                            }}
                            options={{
                                streetViewControl: false,
                                mapTypeControl: false,
                                fullscreenControl: false,
                                zoomControl: true,
                                gestureHandling: 'greedy',
                                styles: [
                                    { featureType: "poi", elementType: "labels", stylers: [{ visibility: "off" }] },
                                    { featureType: "transit", elementType: "labels", stylers: [{ visibility: "off" }] }
                                ]
                            }}
                        >
                            {internalMarker && (
                                <Marker
                                    position={internalMarker}
                                    icon={markerIconStyle}
                                    animation={google.maps.Animation.DROP}
                                    title="Seçili Konum"
                                />
                            )}
                        </GoogleMap>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default SimpleMapComponent;