// AddOrUpdateTicket.tsx - Geliştirilmiş State Management
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Button, Card, Checkbox, Col, Divider, Form, Input, Modal, Badge, Row, Space, Tabs, Upload, Image } from "antd";
import { FC, useEffect, useState, useCallback } from "react";
import { useQueryClient } from "react-query";
import endPoints from "../EndPoints";
import { createTicket, postTicketTransitionsExecute, updateTicketWithPut } from "../Services";
import GeneralSubjectTicket from "@/apps/Common/GeneralSubjectTicket";
import GeneralCustomer from "@/apps/Common/GeneralCustomer";
import GeneralDepartments from "@/apps/Common/Departments/GeneralDepartments";
import { MessageOutlined, HistoryOutlined, FileTextOutlined, EyeOutlined, FileAddOutlined } from "@ant-design/icons";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import { MazakaDatePicker } from "@/apps/Common/MazakaDatePicker";
import { UploadProps } from "antd/lib";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/Reducers";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import dayjs from "dayjs";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import GeneralUsers from "@/apps/Common/GenerralUsers";
import GeneralNotificationWays from "@/apps/Common/GeneralNotificationWays";
import { hanldleSetTicketDetails } from "../ClientSideStates";
import { DeleteOutlined, InboxOutlined, PlusOutlined } from "@ant-design/icons";
import { Typography } from "antd";
import GeneralStatusTicket from "@/apps/Common/GeneralStatusTicket";
import { useGetTicketDetail } from "../ServerSideStates";
import GeneralTicketList from "@/apps/Common/GeneralAllTickets";
import TicketCommentIndex from "./Comments/TicketCommentIndex";
import TicketHistoryIndex from "./TicketHistory/TicketHistoryIndex";
import TicketWatchersIndex from "./TicketWatchs/TicketWatchers";
import Title from "./Title";
import ReusableFileManager, { FileItem } from "@/apps/Common/ReusableFileManager";
import SimpleMapComponent from "@/apps/Common/GoogleMapPin";

const { Text } = Typography;

// Address Info interface
interface AddressAttributeData {
  latitude?: number;
  longitude?: number;
  address?: string;
}

// Initial state değerleri
const INITIAL_ADDRESS_STATE: AddressAttributeData = {
  latitude: undefined,
  longitude: undefined,
  address: ''
};

const INITIAL_FORM_VALUES = {
  NotificationWay: 4,
  Priority: 2,
};

const AddOrUpdateTicket = () => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { userInfoes } = useSelector((state: RootState) => state.profile);
  const { filter, pageType, ticketDetails } = useSelector((state: RootState) => state.ticket);
  const dispatch = useDispatch();
  const { t } = useTranslation();

  // State'ler
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [marker, setMarker] = useState<google.maps.LatLngLiteral | null>(null);
  const [addressInfo, setAddressInfo] = useState<AddressAttributeData>(INITIAL_ADDRESS_STATE);
  const [isFormDirty, setIsFormDirty] = useState(false); // Form değişiklik takibi

  const serverTicketDetails = useGetTicketDetail(ticketDetails?.Id);

  // Dosya uzantısından mime type çıkarma - Memoized
  const getMimeTypeFromFileName = useCallback((fileName: string): string => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg', 'jpeg': 'image/jpeg', 'png': 'image/png', 'gif': 'image/gif',
      'webp': 'image/webp', 'svg': 'image/svg+xml', 'pdf': 'application/pdf',
      'doc': 'application/msword', 'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'xls': 'application/vnd.ms-excel', 'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint', 'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'txt': 'text/plain', 'zip': 'application/zip', 'rar': 'application/x-rar-compressed',
      'mp4': 'video/mp4', 'mp3': 'audio/mpeg', 'wav': 'audio/wav'
    };
    return mimeTypes[extension || ''] || 'application/octet-stream';
  }, []);

  // Tüm state'leri sıfırlama fonksiyonu
  const resetAllStates = useCallback(() => {
    setFileList([]);
    setMarker(null);
    setAddressInfo(INITIAL_ADDRESS_STATE);
    setIsFormDirty(false);
    form.resetFields();
    form.setFieldsValue(INITIAL_FORM_VALUES);
  }, [form]);

  // Server ticket details effect
  useEffect(() => {
    if (serverTicketDetails.data?.Value) {
      dispatch(hanldleSetTicketDetails({ data: serverTicketDetails.data.Value }));
    }
  }, [serverTicketDetails.data?.Value, dispatch]);

  // AttributeData'dan address bilgilerini yükleme - Optimized
  const loadAddressFromAttributeData = useCallback((attributeData: any) => {
    if (!attributeData || (!attributeData.latitude && !attributeData.longitude && !attributeData.address)) {
      return;
    }

    const addressData: AddressAttributeData = {
      latitude: attributeData.latitude ? parseFloat(attributeData.latitude) : undefined,
      longitude: attributeData.longitude ? parseFloat(attributeData.longitude) : undefined,
      address: attributeData.address || ''
    };

    setAddressInfo(addressData);

    if (addressData.latitude && addressData.longitude) {
      setMarker({
        lat: addressData.latitude,
        lng: addressData.longitude
      });
    }

    form.setFieldsValue({ Address: addressData.address });
  }, [form]);

  // AttributeData'ya address bilgilerini kaydetme - Simplified
  const prepareAttributeData = useCallback((currentAttributeData: any = {}) => {
    const attributeData = { ...currentAttributeData };

    // Coordinate varsa ekle
    if (addressInfo.latitude !== undefined && addressInfo.longitude !== undefined) {
      attributeData.latitude = addressInfo.latitude.toString();
      attributeData.longitude = addressInfo.longitude.toString();
    } else {
      // Coordinate yoksa sil
      delete attributeData.latitude;
      delete attributeData.longitude;
    }

    // Address varsa ekle
    if (addressInfo.address?.trim()) {
      attributeData.address = addressInfo.address.trim();
    } else {
      // Address yoksa sil
      delete attributeData.address;
    }

    return attributeData;
  }, [addressInfo]);

  // Harita marker değişikliği - Simplified
  const handleMarkerChange = useCallback((coords: { lat: number; lng: number } | null, address: string) => {
    setMarker(coords);

    const newAddressInfo = {
      latitude: coords?.lat,
      longitude: coords?.lng,
      address: address
    };

    setAddressInfo(newAddressInfo);
    form.setFieldsValue({ Address: address });
    setIsFormDirty(true);
  }, [form]);

  // Address textarea değişikliği
  const handleAddressTextareaChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setAddressInfo(prev => ({ ...prev, address: value }));
    form.setFieldValue('Address', value);
    setIsFormDirty(true);
  }, [form]);

  // Address alanlarını temizleme
  const clearAddressData = useCallback(() => {
    setAddressInfo(INITIAL_ADDRESS_STATE);
    setMarker(null);
    form.setFieldsValue({ Address: '' });
    setIsFormDirty(true);
  }, [form]);

  // Tabs - Memoized
  const tabItems = ticketDetails ? [
    {
      key: '1',
      label: (
        <Space size="small">
          <MessageOutlined style={{ fontSize: '16px' }} />
          <span>Aktiviteler</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketCommentIndex />
        </Card>
      ),
    },
    {
      key: '2',
      label: (
        <Space size="small">
          <HistoryOutlined style={{ fontSize: '16px' }} />
          <span>Geçmiş</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketHistoryIndex />
        </Card>
      ),
    },
    {
      key: '3',
      label: (
        <Space size="small">
          <FileTextOutlined style={{ fontSize: '16px' }} />
          <span>Sistem Logları</span>
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <div style={{ padding: '20px', textAlign: 'center', color: '#8c8c8c' }}>
            <FileTextOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Title level={4} type="secondary">Sistem Logları</Title>
            <p>Sistem logları burada gösterilecek...</p>
          </div>
        </Card>
      ),
    },
    {
      key: '4',
      label: (
        <Space size="small">
          <EyeOutlined style={{ fontSize: '16px' }} />
          <span>Takip Edenler</span>
          {ticketDetails?.Watchlist?.length > 0 && (
            <Badge
              count={ticketDetails.Watchlist.length}
              size="small"
              style={{ backgroundColor: '#52c41a' }}
            />
          )}
        </Space>
      ),
      children: (
        <Card size="small" style={{ minHeight: '400px' }}>
          <TicketWatchersIndex />
        </Card>
      ),
    },
  ] : [];

  // Status değişikliği
  const handlePostTransitionStatus = useCallback(async (transitionId: string) => {
    try {
      await postTicketTransitionsExecute(ticketDetails.Id, transitionId);
      queryClient.resetQueries({
        queryKey: endPoints.getTicketDetail,
        exact: false,
      });
    } catch (error) {
      showErrorCatching(error, null, false, t);
    }
  }, [ticketDetails?.Id, queryClient, t]);

  const priorityOptions = [
    { label: t("ticket.list.low"), value: 1 },
    { label: t("ticket.list.medium"), value: 2 },
    { label: t("ticket.list.high"), value: 3 },
    { label: t("ticket.list.critical"), value: 4 },
  ];

  // Ticket details yüklendiğinde form populate et
  useEffect(() => {
    if (ticketDetails) {
      const data = { ...ticketDetails };
      data["EndDate"] = dayjs(data["EndDate"]);
      data["StatusName"] = ticketDetails?.StatusName;
      data["Departments"] = ticketDetails?.Departments?.map(
        (item: any) => item?.DepartmentId
      );
      form.setFieldsValue(data);

      // Dosyaları yükle
      if (ticketDetails.TicketFiles) {
        const normalizedFiles: FileItem[] = ticketDetails.TicketFiles.map((file: any) => ({
          FileId: file.FileId || file.Id,
          FileName: file.FileName,
          FilePath: file.FilePath,
          MimeType: file.MimeType || getMimeTypeFromFileName(file.FileName),
          FileSizeInBytes: file.FileSizeInBytes
        }));
        setFileList(normalizedFiles);
      }

      // AttributeData'dan address bilgilerini yükle
      if (ticketDetails.AttributeData) {
        try {
          const attributeData = typeof ticketDetails.AttributeData === 'string'
            ? JSON.parse(ticketDetails.AttributeData)
            : ticketDetails.AttributeData;
          loadAddressFromAttributeData(attributeData);
        } catch (error) {
          console.error("AttributeData parse edilemedi:", error);
        }
      }

      setIsFormDirty(false); // Form yüklendiğinde dirty flag'i sıfırla
    } else {
      // Yeni ticket oluşturma modunda state'leri sıfırla
      resetAllStates();
    }
  }, [ticketDetails, getMimeTypeFromFileName, loadAddressFromAttributeData, resetAllStates, form]);

  // Form değişiklik takibi
  const handleFormChange = useCallback(() => {
    setIsFormDirty(true);
  }, []);

  // File list değişiklik takibi
  const handleFileListChange = useCallback((newFileList: FileItem[]) => {
    setFileList(newFileList);
    setIsFormDirty(true);
  }, []);

  // Form submit
  const handleOnFinish = useCallback(async () => {
    try {
      const formValues = form.getFieldsValue();
      formValues["TicketFiles"] = fileList;
      delete formValues["StatusId"];

      // AttributeData'yı hazırla
      const currentAttributeData = ticketDetails?.AttributeData
        ? (typeof ticketDetails.AttributeData === 'string'
          ? JSON.parse(ticketDetails.AttributeData)
          : ticketDetails.AttributeData)
        : {};

      const preparedAttributeData = prepareAttributeData(currentAttributeData);
      formValues["AttributeData"] = preparedAttributeData;

      if (pageType === "customerDetails") {
        formValues["CustomerId"] = filter?.CustomerId;
      }

      let response;
      if (ticketDetails) {
        await updateTicketWithPut({ ...ticketDetails, ...formValues });
      } else {
        response = await createTicket(formValues);
        dispatch(hanldleSetTicketDetails({ data: { Id: response?.Value } }));
      }

      openNotificationWithIcon("success", t("form.transactionSuccessful"));

      // Sadece yeni ticket oluşturulduğunda state'leri sıfırla
      if (!ticketDetails) {
        resetAllStates();
      } else {
        setIsFormDirty(false); // Update işleminde sadece dirty flag'i sıfırla
      }

      queryClient.resetQueries({
        queryKey: endPoints.getTicketListFilter,
        exact: false,
      });

    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  }, [
    form, fileList, ticketDetails, prepareAttributeData, pageType, filter,
    dispatch, t, resetAllStates, queryClient, mazakaForm
  ]);

  return (
    <>
      <MazakaForm
        className="rounded-none ticket-form !rounded-none !border-none !p-0"
        form={form}
        onFinish={handleOnFinish}
        onFieldsChange={handleFormChange} // Form değişiklik takibi
        submitButtonVisible={false}
        initialValues={INITIAL_FORM_VALUES}
      >
        <Row gutter={[16, 16]} className="!rounded-none border-none !p-0">
          {/* Sol Taraf - Ticket Detayları */}
          <Col xs={24} lg={16} className="rounded-none border-0 rounded-none">
            <Space direction="vertical" size="middle" className="rounded-none border-none" style={{ width: '100%' }}>
              <Card
                size="small"
                className="!rounded-none !border-none"
                headStyle={{ backgroundColor: '#fafafa', fontWeight: 600 }}
              >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Row gutter={[8, 8]} >
                    <Col xs={12} >
                      <GeneralSubjectTicket
                        label={t("ticket.list.subject")}
                        placeholder={t("ticket.list.subject")}
                        xs={24}
                        name="SubjectId"
                        className="d-flex !flex-row ps-1"
                        rules={[{ required: true, message: "" }]}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 24 }}
                      />
                    </Col>

                    <Col xs={12}>
                      <MazakaInput
                        label={t("ticket.list.title")}
                        placeholder={t("ticket.list.title")}
                        xs={24}
                        name="Title"
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 24 }}
                        rules={[{ required: true, message: "" }]}
                      />
                    </Col>
                  </Row>

                  <MazakaTextArea
                    xs={24}
                    name="Description"
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 24 }}
                    label={t("ticket.list.description")}
                    placeholder={t("ticket.list.description")}
                    rows={6}
                  />

                  <MazakaTextArea
                    label="Adres"
                    className="h-100"
                    placeholder="Adres bilgisini girin veya harita üzerinden seçin"
                    name="Address"
                    value={addressInfo.address}
                    onChange={handleAddressTextareaChange}
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                  />

                  <Card size="small" className="!rounded-none border-none mt-0">
                    <Row gutter={[0, 0]} className="mt-0">
                      <Col xs={24} className="border-none">
                        <SimpleMapComponent
                          marker={marker}
                          address={addressInfo.address}
                          onMarkerChange={handleMarkerChange}
                          visible={true}
                          showInlineMap={true} // Bu true olursa direkt harita gösterir
                        />
                      </Col>
                    </Row>
                  </Card>
                </Space>

                {/* ReusableFileManager Component Kullanımı */}
                <ReusableFileManager
                  fileList={fileList}
                  onFileListChange={handleFileListChange} // Tracked file change
                  title="Ticket Dosyaları"
                  showCount={true}
                  maxFiles={10}
                  imageSize={{ width: 80, height: 80 }}
                  cardSize="small"
                  className="mt-4"
                  emptyMessage="Ticket dosyası eklemek için + butonuna tıklayın."
                />

                {/* Sadece ticketDetails varsa tabs göster */}
                {ticketDetails && (
                  <Card
                    size="small"
                    className="mt-2 !border-none !rounded-none"
                  >
                    <Tabs
                      defaultActiveKey="1"
                      items={tabItems}
                      type="line"
                      className="ticket-detail-tabs"
                      tabBarStyle={{
                        marginBottom: '5x',
                        borderBottom: '1px solid #f0f0f0'
                      }}
                      tabBarGutter={32}
                      animated={{ inkBar: true, tabPane: true }}
                    />
                  </Card>
                )}
              </Card>
            </Space>
          </Col>

          {/* Sağ Taraf - Form Controls */}
          <Col xs={24} lg={8} className="h-100 ticket-form-attach">
            <Card
              size="small"
              style={{ height: '100%', backgroundColor: '#fafafa' }}
              className="mt-1 rounded-none"
              headStyle={{ backgroundColor: '#fafafa', fontWeight: 600 }}
            >
              <Space direction="vertical" className="rounded-none p-0 m-0" size="middle" style={{ width: '100%', backgroundColor: '#fafafa' }}>
                <Row gutter={8} align="bottom" style={{ width: '100%' }}>
                  {ticketDetails && (
                    <Col flex={1}>
                      <GeneralStatusTicket
                        placeholder={t("ticket.list.status")}
                        xs={24}
                        ticketId={ticketDetails?.Id}
                        className="bg-white"
                        name="StatusName"
                        onChange={handlePostTransitionStatus}
                      />
                    </Col>
                  )}

                  <Col>
                    <Form.Item className="mb-0">
                      <MazakaButton
                        processType={formActions.submitProcessType}
                        htmlType="submit"
                        status="save"
                        className="h-8 max-h-8"
                        style={{ height: '32px', maxHeight: '32px' }}
                      >
                        {ticketDetails ? t("ticket.list.edit") : t("ticket.list.add")}
                        {isFormDirty && " *"} {/* Dirty indicator */}
                      </MazakaButton>
                    </Form.Item>
                  </Col>
                </Row>

                <Divider className="m-1" />

                <GeneralUsers
                  label={t("task.list.assignedUser")}
                  placeholder={t("task.list.assignedUser")}
                  name="UserId"
                  rules={[{ required: true, message: "" }]}
                  xs={24}
                />

                <GeneralDepartments
                  label={t("department.departments")}
                  placeholder={t("department.departments")}
                  multiple
                  name="Departments"
                />

                <GeneralTicketList
                  label="Ana Ticket"
                  placeholder="Ana Ticket"
                  ticketId={ticketDetails?.Id}
                  name="TopTicketId"
                />

                {pageType === "tickets" && (
                  <GeneralCustomer
                    label={t("ticket.list.customer")}
                    placeholder={t("ticket.list.customer")}
                    xs={24}
                    name="CustomerId"
                    rules={[{ required: true, message: "" }]}
                  />
                )}

                {pageType === "tickets" && (
                  <GeneralUsers
                    label={t("ticket.list.watchers")}
                    placeholder={t("ticket.list.watchers")}
                    name="Watchlist"
                    mode="multiple"
                    xs={24}
                  />
                )}

                <MazakaSelect
                  label={t("ticket.list.priority")}
                  placeholder={t("ticket.list.priority")}
                  xs={24}
                  name="Priority"
                  options={priorityOptions}
                  rules={[{ required: true, message: "" }]}
                />

                <MazakaDatePicker
                  label={t("ticket.list.endDate")}
                  xs={24}
                  name="EndDate"
                  disablePastDates
                  rules={[{ required: true, message: "" }]}
                />

                <GeneralNotificationWays
                  label={t("notificationWay.notificationMethod")}
                  labelCol={{ span: 24 }}
                  wrapperCol={{ span: 24 }}
                  placeholder={t("notificationWay.notificationMethod")}
                  name="NotificationWayId"
                  xs={24}
                />
              </Space>
            </Card>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateTicket;