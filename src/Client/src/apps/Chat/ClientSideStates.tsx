import { createSlice } from "@reduxjs/toolkit";

const InitialState: { userChatFilter: any; selectedChatItem: null | any,chatDetailsFilter:any;selectedMessageItem:null|any;
  } = {
  userChatFilter: {
    PageNumber: 1,
    PageSize: 20,
    status: 1,
  },
  chatDetailsFilter: {
    PageNumber: 1,
    PageSize: 20,
  
  },
  selectedChatItem: null,
  selectedMessageItem:null
};

const chatSlice = createSlice({
  name: "ChatSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetUserChatFilter: (state, action) => {
      let data = action.payload;
      state.userChatFilter = data.filter;
    },
    handleSetChatDetailsFilter: (state, action) => {
      let data = action.payload;
      state.chatDetailsFilter= data.filter;
    },

    hanldleSetSelectedUserListChatItem: (state, action) => {
      let data = action.payload;
      state.selectedChatItem = data.data;
    },
    handleSetSelectedChatMessageItem: (state, action) => {
      let data = action.payload;
      state.selectedMessageItem = data.data;
    },

    handleResetAllFieldsChat: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetUserChatFilter: (state) => {
      state.userChatFilter = {
        PageNumber: 1,
        PageSize: 20,
      };
    },
  },
});

export const {
  handleResetAllFieldsChat,
  handleResetUserChatFilter,
  hanldleSetUserChatFilter,
  hanldleSetSelectedUserListChatItem,
  handleSetChatDetailsFilter,
  handleSetSelectedChatMessageItem
} = chatSlice.actions;
export default chatSlice;
