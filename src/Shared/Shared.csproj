﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="MediatR.Contracts" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.AspNetCore" />
    <PackageReference Include="Serilog.Sinks.Seq" />
    <PackageReference Include="Serilog.Sinks.File" />
    <PackageReference Include="FluentValidation" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" />
    <PackageReference Include="MailKit" />
    <PackageReference Include="Mimekit" />
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" />
    <PackageReference Include="Microsoft.AspNetCore.OData" />
    <PackageReference Include="Microsoft.OData.ModelBuilder" />
    <PackageReference Include="libphonenumber-csharp" />
    <PackageReference Include="Microsoft.AspNetCore.JsonPatch" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Infrastructure/Localization/Resources/*.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Link>Infrastructure/Localization/Resources/%(Filename)%(Extension)</Link>
    </Content>
  </ItemGroup>
</Project>