using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;
using Shared.Application.Extensions;
using Shared.Contracts;
using Tasks.Application.Abstractions;

namespace Tasks.Application.Comments.ListComments;

public class ListCommentsQueryHandler(
    ITaskDbContext dbContext,
    ISharedUserService userService
) : IRequestHandler<ListCommentsQuery, Result<List<CommentDto>>>
{
    private readonly ITaskDbContext _dbContext = dbContext;
    private readonly ISharedUserService _userService = userService;

    public async Task<Result<List<CommentDto>>> Handle(ListCommentsQuery request, CancellationToken cancellationToken)
    {
        var task = await _dbContext.Tasks.FirstOrDefaultAsync(t => t.Id == request.TaskId, cancellationToken);
        if (task == null)
        {
            return Result.Failure<List<CommentDto>>("404", "Görev bulunamadı.");
        }
        var comments = await _dbContext.Comments
            .Where(c => c.TaskId == request.TaskId)
            .ApplySorting(request.SortProperty, request.SortType)
            .ToListAsync(cancellationToken);
        if (!comments.Any())
        {
            return Result.Success(new List<CommentDto>());
        }
        var userIds = comments.Select(c => c.UserId).Distinct().ToList();
        var users = await _userService.GetUsersByIdsAsync(userIds);
        var userDict = users.ToDictionary(u => u.Id, u => $"{u.Name} {u.Surname}");
        var commentDtos = comments.Select(c => new CommentDto(
            c.Id,
            c.TaskId,
            c.UserId,
            userDict.ContainsKey(c.UserId) ? userDict[c.UserId] : "Bilinmeyen Kullanıcı",
            c.Content,
            c.InsertDate
        )).ToList();
        return Result.Success(commentDtos);
    }
}
