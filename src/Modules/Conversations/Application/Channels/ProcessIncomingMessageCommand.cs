using Conversations.Domain.Chats;
using MediatR;
using Shared.Application;

namespace Conversations.Application.Channels;

public record ProcessIncomingMessageCommand : IRequest<Result<Guid>>
{
    public ChatChannel Channel { get; init; }
    public string ExternalChatId { get; init; }
    public string BaseChatId { get; init; }
    public string ExternalMessageId { get; init; }
    public Guid? CustomerId { get; init; }
    public string CustomerName { get; init; }
    public string Content { get; init; }
    public MessageContentType ContentType { get; init; }
    public DateTime SentAt { get; init; }
    public string Sender { get; init; }
    public string? MetaData { get; init; }
    public List<IncomingAttachment>? Attachments { get; init; }
}

public record IncomingAttachment
{
    public string Url { get; init; }
    public AttachmentType Type { get; init; }
    public string? FileName { get; init; }
    public string? ContentType { get; init; }
    public long? FileSize { get; init; }
}
