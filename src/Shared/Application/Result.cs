using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Mvc;

namespace Shared.Application;

public class Result
{
    public Result(bool isSuccess, Error? error)
    {
        if (isSuccess && error != Error.None && error != null ||
            !isSuccess && (error == Error.None || error == null))
        {
            throw new ArgumentException("Invalid error", nameof(error));
        }

        IsSuccess = isSuccess;
        Error = error;
    }

    public bool IsSuccess { get; }

    public Error? Error { get; }

    public static Result Success() =>
        new(true, null);

    public static Result<TValue> Success<TValue>(TValue value) =>
        new(value, true, null);

    public static Result Validation(string message) =>
        new(false, new Error("400", message, ErrorType.Validation));

    public static Result Validation(Error error) =>
        new(false, error);

    public static Result Failure(Error? error) =>
        new(false, error);

    public static Result<TValue> Failure<TValue>(Error error) =>
        new(default, false, error);

    public static Result Validation(ValidationProblemDetails details) =>
        new(false, new Error("400", details.Detail ?? "Validation Error", ErrorType.Validation, details));

    public static Result Failure(string error) =>
        new(false, new Error("500", error, ErrorType.Failure));

    public static Result Failure(string code, string error) =>
        new(false, new Error(code, error, ErrorType.Failure));

    public static Result<TValue> Failure<TValue>(string error) =>
        new(default, false, new Error("500", error, ErrorType.Failure));

    public static Result<TValue> Failure<TValue>(string code, string error) =>
        new(default, false, new Error(code, error, ErrorType.Failure));
}

public class Result<TValue>(
    TValue? value,
    bool isSuccess,
    Error? error
) : Result(isSuccess, error)
{
    private readonly TValue? _value = value;

    [NotNull]
    public TValue Value => IsSuccess
        ? _value!
        : throw new InvalidOperationException("The value of a failure result can't be accessed.");

    public static implicit operator Result<TValue>(TValue? value) =>
        value is not null ? Success(value) : Failure<TValue>(Error.NullValue);

    public static Result<TValue> Validation(string message) =>
        new(default, false, new Error("400", message, ErrorType.Validation));

    public static Result<TValue> Validation(Error error) =>
        new(default, false, error);

    public static Result<TValue> Validation(string message, TValue value) =>
        new(value, false, new Error("400", message, ErrorType.Validation));

    public static Result<TValue> Validation(Error error, TValue value) =>
        new(value, false, error);

    public static Result<TValue> Validation(ValidationProblemDetails details, TValue value) =>
        new(value, false, new Error("400", details.Detail ?? "Validation Error", ErrorType.Validation, details));

}

public class PagedResult<TValue>(
    List<TValue>? value,
    bool isSuccess = true,
    Error? error = null
) : Result(isSuccess, error)
{
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int Count { get; set; }
    public int FilteredCount { get; set; }
    public string SortProperty { get; set; } = string.Empty;
    public string SortType { get; set; } = string.Empty;

    private readonly List<TValue>? _value = value;

    [NotNull]
    public List<TValue> Value => IsSuccess
        ? _value!
        : throw new InvalidOperationException("The value of a failure result can't be accessed.");

    public static implicit operator PagedResult<TValue>(List<TValue>? value) =>
        value is not null ? Success(value) : Failure<TValue>(Error.NullValue);

    public static Result<TValue> ValidationFailure(Error error) =>
        new(default, false, error);

    public static PagedResult<TValue> Success(List<TValue>? value) =>
        new(value, true, null);

    public static PagedResult<TValue> Failure<TValue>(Error? error) =>
        new(default, false, error);

    public static PagedResult<TValue> Failure(string error) =>
        new(default, false, new Error("500", error, ErrorType.Failure));
    public static PagedResult<TValue> Failure(string code, string error) =>
        new(default, false, new Error(code, error, ErrorType.Failure));
}
