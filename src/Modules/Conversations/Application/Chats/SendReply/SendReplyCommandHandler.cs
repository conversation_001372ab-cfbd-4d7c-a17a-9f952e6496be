using System.Text.Json;
using Conversations.Application.Abstractions;
using Conversations.Domain.Chats;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Shared.Application;

namespace Conversations.Application.Chats.SendReply;

public class SendReplyCommandHandler(
    IConversationDbContext dbContext,
    IMessageSenderFactory messageSenderFactory
) : IRequestHandler<SendReplyCommand, Result<Guid>>
{
    private readonly IConversationDbContext _dbContext = dbContext;
    private readonly IMessageSenderFactory _messageSenderFactory = messageSenderFactory;

    public async Task<Result<Guid>> Handle(SendReplyCommand request, CancellationToken cancellationToken)
    {
        var chat = await _dbContext.Chat
            .Include(c => c.Messages)
            .FirstOrDefaultAsync(c => c.Id == request.ChatId, cancellationToken);

        if (chat == null)
        {
            return Result.Failure<Guid>($"Chat with ID {request.ChatId} not found");
        }

        // Get reply-to message if specified
        string? replyToExternalId = null;
        if (request.ReplyToMessageId.HasValue)
        {
            var replyToMessage = await _dbContext.ChatMessage
                .FirstOrDefaultAsync(m => m.Id == request.ReplyToMessageId.Value, cancellationToken);

            if (replyToMessage != null)
            {
                replyToExternalId = replyToMessage.ExternalId;
            }
        }

        // Create the message in our system
        var message = chat.AddMessage(
            request.Content,
            MessageDirection.Outgoing,
            request.SenderId,
            request.ContentType,
            request.ReplyToMessageId);

        // Add metadata if replying to a message
        if (replyToExternalId != null)
        {
            var metadata = new { ReplyToMessageId = replyToExternalId };
            message.SetMetaData(JsonSerializer.Serialize(metadata));
        }

        // Add attachments if any
        if (request.Attachments != null && request.Attachments.Any())
        {
            foreach (var attachment in request.Attachments)
            {
                message.AddAttachment(
                    attachment.Url,
                    attachment.Type,
                    attachment.FileName,
                    attachment.ContentType,
                    attachment.FileSize);
            }
        }

        // Save to get the message ID
        await _dbContext.SaveChangesAsync(cancellationToken);

        // Send the message through the appropriate channel
        var messageSender = _messageSenderFactory.GetSender(chat.Channel);
        var sendResult = await messageSender.SendMessageAsync(new SendMessageRequest
        {
            MessageId = message.Id,
            ChatId = chat.Id,
            BaseChatId = chat.BaseChatId,
            RecipientIdentifier = chat.ExternalId,
            Content = request.Content,
            ContentType = request.ContentType,
            ReplyToExternalId = replyToExternalId,
            Attachments = request.Attachments?.Select(a => new AttachmentRequest
            {
                Url = a.Url,
                Type = a.Type,
                FileName = a.FileName,
                ContentType = a.ContentType,
                FileSize = a.FileSize
            }).ToList()
        });

        if (sendResult.Success)
        {
            // Update the message with the external ID
            message.SetExternalId(sendResult.ExternalMessageId);
            await _dbContext.SaveChangesAsync(cancellationToken);
        }
        else
        {
            // Log the error but still return success since the message is created in our system
            // In a real system, you might want to queue this for retry
            // For now, we'll just return the error
            return Result.Failure<Guid>($"Message created but failed to send: {sendResult.ErrorMessage}");
        }

        return Result.Success(message.Id);
    }
}
