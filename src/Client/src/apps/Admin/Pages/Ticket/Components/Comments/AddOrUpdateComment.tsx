import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import { createTicketComment } from "../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import endPoints from "../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import MazakaTextEditor from "@/apps/Common/MazakaTextEditor";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import ReusableFileManager, { FileItem } from "@/apps/Common/ReusableFileManager";
import { useState } from "react";

const AddOrUpdateComment = () => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);

  // File management state
  const [fileList, setFileList] = useState<FileItem[]>([]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    formValues["TicketId"] = ticketDetails?.Id;

    // Dosyaları forma ekle
    if (fileList.length > 0) {
      formValues["Files"] = fileList;
    }

    try {
      await createTicketComment(formValues);

      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      openNotificationWithIcon("success", t("form.transactionSuccessful"));

      queryClient.resetQueries({
        queryKey: endPoints.getTicketComments,
        exact: false,
      });

      // Form ve dosyaları temizle
      form.resetFields();
      setFileList([]);

    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[0, 20]}>
          {/* Text Editor */}
          <Col xs={24}>
            <MazakaTextEditor
              className="!m-0"
              name={"Comment"}
              xs={24}
              label={t("ticket.list.explainActionYouPerformed")}
              placeholder={t("ticket.list.explainActionYouPerformed")}
              form={form}
            />
          </Col>

          {/* File Manager */}
          <Col xs={24}>
            <ReusableFileManager
              fileList={fileList}
              onFileListChange={setFileList}
              title="Yorum Dosyaları"
              showCount={true}
              maxFiles={5}
              imageSize={{ width: 70, height: 70 }}
              cardSize="small"
              emptyMessage="Yorum için dosya eklemek istiyorsanız + butonuna tıklayın."
              showEmptyMessage={false} // Yorumda boş mesaj gösterme
            />
          </Col>

          {/* Submit Button */}
          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="button"
              onClick={async () => {
                await form.validateFields();
                handleOnFinish()
              }}
            >
              {t("ticket.list.save")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateComment;