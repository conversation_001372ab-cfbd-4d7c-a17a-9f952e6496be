using Conversations.Domain.Chats;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Shared.Application;
using Shared.Endpoints;

namespace Conversations.Application.Chats.ListChats;

internal sealed class ListChatsEndpoint : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapGet("/api/v1/conversations/chats", async (
            Guid? customerId,
            Guid? assignedUserId,
            ChatStatus? status,
            ChatChannel? channel,
            string? searchTerm,
            int? pageNumber,
            int? pageSize,
            IMediator mediator) =>
        {
            var query = new ListChatsQuery
            (
                customerId,
                assignedUserId,
                status,
                channel,
                searchTerm,
                pageNumber ?? 1,
                pageSize ?? 20
            );
            var result = await mediator.Send(query);
            return result.Match(Results.Ok, CustomResults.Problem);
        })
        .WithTags("Conversations.Chats")
        .WithGroupName("apiv1")
        .ProducesProblem(StatusCodes.Status400BadRequest)
        .Produces<PagedResult<ChatDto>>(StatusCodes.Status200OK)
        .RequireAuthorization("Conversations.Chats");
    }
}
