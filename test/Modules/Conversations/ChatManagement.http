@baseUrl = https://localhost:5001
@token = {{$dotenv TOKEN}}
@chatId = 00000000-0000-0000-0000-000000000000
@messageId = 00000000-0000-0000-0000-000000000000

### List All Chats
GET {{baseUrl}}/api/v1/conversations/chats?pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### Create Test Chat
POST {{baseUrl}}/api/v1/conversations/chats
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "customerId": null,
  "customerName": "Test Customer",
  "channel": 1,
  "externalId": "test-chat-{{$randomInt}}",
  "baseChatId": "base-chat-{{$randomInt}}",
  "title": "Test Chat for Reply Messages"
}

### Add Message to Chat
POST {{baseUrl}}/api/v1/conversations/chats/{{chatId}}/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "Hello, this is a test message",
  "direction": 1,
  "senderId": "customer123",
  "contentType": 1
}

### Add Reply Message
POST {{baseUrl}}/api/v1/conversations/chats/{{chatId}}/messages
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "This is a reply to the previous message",
  "direction": 2,
  "senderId": "agent456",
  "contentType": 1,
  "replyToMessageId": "{{messageId}}"
}

### Send Reply via SendReply endpoint
POST {{baseUrl}}/api/v1/conversations/chats/{{chatId}}/reply
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "content": "This is a reply using SendReply endpoint",
  "senderId": "agent789",
  "contentType": 1,
  "replyToMessageId": "{{messageId}}"
}

### Get Chat with Messages (to see reply relationships)
GET {{baseUrl}}/api/v1/conversations/chats/{{chatId}}
Authorization: Bearer {{token}}

### Close Chat
PUT {{baseUrl}}/api/v1/conversations/chats/{{chatId}}/close
Authorization: Bearer {{token}}
Content-Type: application/json

### Archive Chat  
PUT {{baseUrl}}/api/v1/conversations/chats/{{chatId}}/archive
Authorization: Bearer {{token}}
Content-Type: application/json
