using System.Linq.Expressions;
using System.Reflection;

namespace Shared.Application.Extensions;

/// <summary>
/// IQueryable için sıralama extension metodları
/// </summary>
public static class QueryableExtensions
{
    /// <summary>
    /// URL'den gelen SortProperty ve SortType parametrelerine göre queryable'ı sıralar
    /// </summary>
    /// <typeparam name="T">Entity tipi</typeparam>
    /// <param name="query">Sıralanacak queryable</param>
    /// <param name="sortProperty">Sıralama yapılacak property adı</param>
    /// <param name="sortType">Sıralama tipi (asc/desc)</param>
    /// <param name="defaultSortProperty">Varsayılan sıralama property'si</param>
    /// <param name="defaultSortType">Varsayılan sıralama tipi</param>
    /// <returns>Sıralanmış queryable</returns>
    public static IQueryable<T> ApplySorting<T>(
        this IQueryable<T> query,
        string? sortProperty,
        string? sortType,
        string defaultSortProperty = "InsertDate",
        string defaultSortType = "desc")
    {
        // Parametreleri normalize et
        var property = string.IsNullOrWhiteSpace(sortProperty) ? defaultSortProperty : sortProperty.Trim();
        var type = string.IsNullOrWhiteSpace(sortType) ? defaultSortType : sortType.Trim().ToLowerInvariant();

        // Sıralama tipini kontrol et
        var isDescending = type == "desc" || type == "descending";

        return query.OrderByProperty(property, isDescending);
    }

    /// <summary>
    /// Property adına göre dinamik sıralama yapar
    /// </summary>
    /// <typeparam name="T">Entity tipi</typeparam>
    /// <param name="query">Sıralanacak queryable</param>
    /// <param name="propertyName">Property adı</param>
    /// <param name="descending">Azalan sıralama yapılacak mı</param>
    /// <returns>Sıralanmış queryable</returns>
    public static IQueryable<T> OrderByProperty<T>(
        this IQueryable<T> query,
        string propertyName,
        bool descending = false)
    {
        if (string.IsNullOrWhiteSpace(propertyName))
        {
            return query;
        }

        var entityType = typeof(T);
        var property = GetPropertyInfo(entityType, propertyName);

        if (property == null)
        {
            return query; // Property bulunamazsa sıralama yapma
        }

        var parameter = Expression.Parameter(entityType, "x");
        var propertyAccess = Expression.MakeMemberAccess(parameter, property);
        var orderByExpression = Expression.Lambda(propertyAccess, parameter);

        var methodName = descending ? "OrderByDescending" : "OrderBy";
        var resultExpression = Expression.Call(
            typeof(Queryable),
            methodName,
            [entityType, property.PropertyType],
            query.Expression,
            Expression.Quote(orderByExpression));

        return query.Provider.CreateQuery<T>(resultExpression);
    }

    /// <summary>
    /// Property bilgisini case-insensitive olarak getirir
    /// </summary>
    /// <param name="type">Entity tipi</param>
    /// <param name="propertyName">Property adı</param>
    /// <returns>PropertyInfo veya null</returns>
    private static PropertyInfo? GetPropertyInfo(Type type, string propertyName)
    {
        return type.GetProperty(propertyName, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);
    }

    /// <summary>
    /// Sayfalama uygular
    /// </summary>
    /// <typeparam name="T">Entity tipi</typeparam>
    /// <param name="query">Sayfalanacak queryable</param>
    /// <param name="pageNumber">Sayfa numarası (1'den başlar)</param>
    /// <param name="pageSize">Sayfa boyutu (-1 tüm verileri getirir)</param>
    /// <returns>Sayfalanmış queryable</returns>
    public static IQueryable<T> ApplyPaging<T>(
        this IQueryable<T> query,
        int pageNumber,
        int pageSize)
    {
        // PageSize -1 ise tüm verileri getir (sayfalama yapma)
        if (pageSize == -1)
        {
            return query;
        }

        if (pageNumber < 1)
        {
            pageNumber = 1;
        }
        if (pageSize < 1)
        {
            pageSize = 10;
        }

        return query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize);
    }
}
